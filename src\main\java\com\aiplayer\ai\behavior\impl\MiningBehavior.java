package com.aiplayer.ai.behavior.impl;

import com.aiplayer.ai.behavior.AIBehavior;
import com.aiplayer.ai.behavior.AIBehaviorType;
import com.aiplayer.entity.AIPlayerEntity;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.world.World;
import org.jetbrains.annotations.Nullable;

import java.util.*;

/**
 * 挖矿行为实现
 * AI玩家自动寻找和挖掘矿物
 */
public class MiningBehavior extends AIBehavior {
    
    private static final int SEARCH_RANGE = 16;
    private static final int MAX_MINING_DEPTH = 64;
    private static final Set<Block> VALUABLE_ORES = Set.of(
        Blocks.COAL_ORE, Blocks.DEEPSLATE_COAL_ORE,
        Blocks.IRON_ORE, Blocks.DEEPSLATE_IRON_ORE,
        Blocks.GOLD_ORE, Blocks.DEEPSLATE_GOLD_ORE,
        Blocks.DIAMOND_ORE, Blocks.DEEPSLATE_DIAMOND_ORE,
        Blocks.EMERALD_ORE, Blocks.DEEPSLATE_EMERALD_ORE,
        Blocks.LAPIS_ORE, Blocks.DEEPSLATE_LAPIS_ORE,
        Blocks.REDSTONE_ORE, Blocks.DEEPSLATE_REDSTONE_ORE,
        Blocks.COPPER_ORE, Blocks.DEEPSLATE_COPPER_ORE
    );
    
    private BlockPos currentMiningTarget;
    private Queue<BlockPos> miningQueue;
    private long lastSearchTime;
    private int miningProgress;
    private boolean isDigging;
    
    public MiningBehavior(AIPlayerEntity aiPlayer) {
        super(aiPlayer);
        this.miningQueue = new LinkedList<>();
        this.lastSearchTime = 0;
        this.miningProgress = 0;
        this.isDigging = false;
    }
    
    @Override
    public AIBehaviorType getBehaviorType() {
        return AIBehaviorType.MINING;
    }
    
    @Override
    protected void onStart() {
        miningQueue.clear();
        currentMiningTarget = null;
        isDigging = false;
        miningProgress = 0;
        
        // 确保AI玩家有挖掘工具
        ensureMiningTools();
    }
    
    @Override
    protected void onStop() {
        stopMoving();
        isDigging = false;
        miningProgress = 0;
    }
    
    @Override
    protected void onTick() {
        long currentTime = aiPlayer.getWorld().getTime();
        
        // 定期搜索新的矿物
        if (currentTime - lastSearchTime > 100) { // 每5秒搜索一次
            searchForOres();
            lastSearchTime = currentTime;
        }
        
        // 处理当前挖掘任务
        if (currentMiningTarget != null) {
            processMining();
        } else if (!miningQueue.isEmpty()) {
            // 获取下一个挖掘目标
            currentMiningTarget = miningQueue.poll();
            if (currentMiningTarget != null) {
                moveToTarget();
            }
        }
    }
    
    @Override
    protected void onTargetChanged(@Nullable BlockPos newTarget) {
        if (newTarget != null) {
            // 添加到挖掘队列
            miningQueue.offer(newTarget);
        }
    }
    
    @Override
    protected void onActionRequested(BlockPos pos, String action) {
        switch (action.toLowerCase()) {
            case "mine":
                setTarget(pos);
                break;
            case "search":
                searchForOres();
                break;
            case "stop":
                miningQueue.clear();
                currentMiningTarget = null;
                stopMoving();
                break;
            default:
                break;
        }
    }
    
    /**
     * 搜索矿物
     */
    private void searchForOres() {
        BlockPos playerPos = aiPlayer.getBlockPos();
        World world = aiPlayer.getWorld();
        List<BlockPos> foundOres = new ArrayList<>();
        
        // 在搜索范围内寻找矿物
        for (int x = -SEARCH_RANGE; x <= SEARCH_RANGE; x++) {
            for (int y = -SEARCH_RANGE; y <= SEARCH_RANGE; y++) {
                for (int z = -SEARCH_RANGE; z <= SEARCH_RANGE; z++) {
                    BlockPos pos = playerPos.add(x, y, z);
                    
                    // 检查Y坐标限制
                    if (pos.getY() < world.getBottomY() || 
                        pos.getY() > playerPos.getY() + 5) {
                        continue;
                    }
                    
                    BlockState state = world.getBlockState(pos);
                    if (VALUABLE_ORES.contains(state.getBlock())) {
                        foundOres.add(pos);
                    }
                }
            }
        }
        
        // 按距离排序
        foundOres.sort(Comparator.comparingDouble(pos -> 
            playerPos.getSquaredDistance(pos)));
        
        // 添加到挖掘队列
        for (BlockPos ore : foundOres) {
            if (!miningQueue.contains(ore)) {
                miningQueue.offer(ore);
            }
        }
    }
    
    /**
     * 处理挖掘过程
     */
    private void processMining() {
        if (currentMiningTarget == null) {
            return;
        }
        
        World world = aiPlayer.getWorld();
        BlockState targetState = world.getBlockState(currentMiningTarget);
        
        // 检查目标是否仍然存在
        if (!VALUABLE_ORES.contains(targetState.getBlock())) {
            currentMiningTarget = null;
            isDigging = false;
            miningProgress = 0;
            return;
        }
        
        // 检查是否在挖掘范围内
        if (!isNearTarget(2.0)) {
            moveToTarget();
            isDigging = false;
            miningProgress = 0;
            return;
        }
        
        // 开始挖掘
        if (!isDigging) {
            isDigging = true;
            miningProgress = 0;
            stopMoving();
        }
        
        // 挖掘进度
        miningProgress++;
        
        // 计算挖掘所需时间（基于方块硬度和工具）
        float hardness = targetState.getHardness(world, currentMiningTarget);
        int requiredTicks = Math.max(1, (int)(hardness * 20)); // 至少1tick
        
        // 完成挖掘
        if (miningProgress >= requiredTicks) {
            mineBlock(currentMiningTarget);
            currentMiningTarget = null;
            isDigging = false;
            miningProgress = 0;
        }
    }
    
    /**
     * 挖掘方块
     */
    private void mineBlock(BlockPos pos) {
        World world = aiPlayer.getWorld();
        BlockState state = world.getBlockState(pos);
        
        // 破坏方块并掉落物品
        world.breakBlock(pos, true, aiPlayer);
        
        // 寻找周围的相同矿物
        for (Direction direction : Direction.values()) {
            BlockPos adjacentPos = pos.offset(direction);
            BlockState adjacentState = world.getBlockState(adjacentPos);
            
            if (VALUABLE_ORES.contains(adjacentState.getBlock()) && 
                !miningQueue.contains(adjacentPos)) {
                miningQueue.offer(adjacentPos);
            }
        }
    }
    
    /**
     * 确保AI玩家有挖掘工具
     */
    private void ensureMiningTools() {
        // 检查是否有镐子
        boolean hasPickaxe = false;
        for (int i = 0; i < aiPlayer.getInventory().size(); i++) {
            ItemStack stack = aiPlayer.getInventory().getStack(i);
            if (stack.getItem() == Items.IRON_PICKAXE || 
                stack.getItem() == Items.DIAMOND_PICKAXE ||
                stack.getItem() == Items.STONE_PICKAXE ||
                stack.getItem() == Items.WOODEN_PICKAXE) {
                hasPickaxe = true;
                break;
            }
        }
        
        // 如果没有镐子，给一个铁镐
        if (!hasPickaxe) {
            aiPlayer.getInventory().insertStack(new ItemStack(Items.IRON_PICKAXE));
        }
    }
    
    /**
     * 获取当前挖掘目标
     */
    public BlockPos getCurrentMiningTarget() {
        return currentMiningTarget;
    }
    
    /**
     * 获取挖掘队列大小
     */
    public int getMiningQueueSize() {
        return miningQueue.size();
    }
    
    /**
     * 检查是否正在挖掘
     */
    public boolean isDigging() {
        return isDigging;
    }
    
    /**
     * 获取挖掘进度
     */
    public int getMiningProgress() {
        return miningProgress;
    }
    
    @Override
    protected void writeCustomDataToNbt(net.minecraft.nbt.NbtCompound nbt) {
        if (currentMiningTarget != null) {
            nbt.putLong("CurrentMiningTarget", currentMiningTarget.asLong());
        }
        nbt.putLong("LastSearchTime", lastSearchTime);
        nbt.putInt("MiningProgress", miningProgress);
        nbt.putBoolean("IsDigging", isDigging);
        
        // 保存挖掘队列
        long[] queueArray = miningQueue.stream().mapToLong(BlockPos::asLong).toArray();
        nbt.putLongArray("MiningQueue", queueArray);
    }
    
    @Override
    protected void readCustomDataFromNbt(net.minecraft.nbt.NbtCompound nbt) {
        if (nbt.contains("CurrentMiningTarget")) {
            currentMiningTarget = BlockPos.fromLong(nbt.getLong("CurrentMiningTarget"));
        }
        lastSearchTime = nbt.getLong("LastSearchTime");
        miningProgress = nbt.getInt("MiningProgress");
        isDigging = nbt.getBoolean("IsDigging");
        
        // 读取挖掘队列
        if (nbt.contains("MiningQueue")) {
            long[] queueArray = nbt.getLongArray("MiningQueue");
            miningQueue.clear();
            for (long pos : queueArray) {
                miningQueue.offer(BlockPos.fromLong(pos));
            }
        }
    }
}
