package com.aiplayer.command;

import com.aiplayer.MinecraftAIPlayerMod;
import com.aiplayer.ai.behavior.AIBehaviorType;
import com.aiplayer.entity.AIPlayerEntity;
import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import net.fabricmc.fabric.api.command.v2.CommandRegistrationCallback;
import net.minecraft.command.CommandRegistryAccess;
import net.minecraft.command.argument.BlockPosArgumentType;
import net.minecraft.command.argument.EntityArgumentType;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;

/**
 * AI玩家命令系统
 * 提供各种命令来控制和管理AI玩家
 */
public class AIPlayerCommands {
    
    /**
     * 注册所有AI玩家相关命令
     */
    public static void register(CommandDispatcher<ServerCommandSource> dispatcher, 
                               CommandRegistryAccess registryAccess, 
                               CommandManager.RegistrationEnvironment environment) {
        
        dispatcher.register(CommandManager.literal("aiplayer")
            .requires(source -> source.hasPermissionLevel(2)) // 需要OP权限
            .then(CommandManager.literal("spawn")
                .then(CommandManager.argument("name", StringArgumentType.string())
                    .then(CommandManager.argument("behavior", StringArgumentType.string())
                        .suggests((context, builder) -> {
                            for (AIBehaviorType type : AIBehaviorType.values()) {
                                builder.suggest(type.name().toLowerCase());
                            }
                            return builder.buildFuture();
                        })
                        .executes(AIPlayerCommands::spawnAIPlayer)
                        .then(CommandManager.argument("pos", BlockPosArgumentType.blockPos())
                            .executes(AIPlayerCommands::spawnAIPlayerAtPos)))))
            
            .then(CommandManager.literal("remove")
                .then(CommandManager.argument("target", EntityArgumentType.entity())
                    .executes(AIPlayerCommands::removeAIPlayer)))
            
            .then(CommandManager.literal("behavior")
                .then(CommandManager.argument("target", EntityArgumentType.entity())
                    .then(CommandManager.argument("behavior", StringArgumentType.string())
                        .suggests((context, builder) -> {
                            for (AIBehaviorType type : AIBehaviorType.values()) {
                                builder.suggest(type.name().toLowerCase());
                            }
                            return builder.buildFuture();
                        })
                        .executes(AIPlayerCommands::setBehavior))))
            
            .then(CommandManager.literal("goto")
                .then(CommandManager.argument("target", EntityArgumentType.entity())
                    .then(CommandManager.argument("pos", BlockPosArgumentType.blockPos())
                        .executes(AIPlayerCommands::gotoPosition))))
            
            .then(CommandManager.literal("action")
                .then(CommandManager.argument("target", EntityArgumentType.entity())
                    .then(CommandManager.argument("action", StringArgumentType.string())
                        .then(CommandManager.argument("pos", BlockPosArgumentType.blockPos())
                            .executes(AIPlayerCommands::performAction)))))
            
            .then(CommandManager.literal("pause")
                .then(CommandManager.argument("target", EntityArgumentType.entity())
                    .executes(AIPlayerCommands::pauseAIPlayer)))
            
            .then(CommandManager.literal("resume")
                .then(CommandManager.argument("target", EntityArgumentType.entity())
                    .executes(AIPlayerCommands::resumeAIPlayer)))
            
            .then(CommandManager.literal("list")
                .executes(AIPlayerCommands::listAIPlayers))
            
            .then(CommandManager.literal("info")
                .then(CommandManager.argument("target", EntityArgumentType.entity())
                    .executes(AIPlayerCommands::showAIPlayerInfo)))
        );
    }
    
    /**
     * 生成AI玩家
     */
    private static int spawnAIPlayer(CommandContext<ServerCommandSource> context) throws CommandSyntaxException {
        ServerCommandSource source = context.getSource();
        String name = StringArgumentType.getString(context, "name");
        String behaviorStr = StringArgumentType.getString(context, "behavior");
        
        try {
            AIBehaviorType behavior = AIBehaviorType.valueOf(behaviorStr.toUpperCase());
            Vec3d pos = source.getPosition();
            
            AIPlayerEntity aiPlayer = MinecraftAIPlayerMod.getInstance().getAIPlayerManager()
                .spawnAIPlayer(source.getWorld(), pos.x, pos.y, pos.z, name, behavior.name());
            
            if (aiPlayer != null) {
                if (source.getEntity() instanceof ServerPlayerEntity player) {
                    aiPlayer.setOwnerId(player.getUuid());
                }
                source.sendFeedback(() -> Text.literal("成功生成AI玩家: " + name), true);
                return 1;
            } else {
                source.sendError(Text.literal("生成AI玩家失败"));
                return 0;
            }
        } catch (IllegalArgumentException e) {
            source.sendError(Text.literal("无效的行为类型: " + behaviorStr));
            return 0;
        }
    }
    
    /**
     * 在指定位置生成AI玩家
     */
    private static int spawnAIPlayerAtPos(CommandContext<ServerCommandSource> context) throws CommandSyntaxException {
        ServerCommandSource source = context.getSource();
        String name = StringArgumentType.getString(context, "name");
        String behaviorStr = StringArgumentType.getString(context, "behavior");
        BlockPos pos = BlockPosArgumentType.getBlockPos(context, "pos");
        
        try {
            AIBehaviorType behavior = AIBehaviorType.valueOf(behaviorStr.toUpperCase());
            
            AIPlayerEntity aiPlayer = MinecraftAIPlayerMod.getInstance().getAIPlayerManager()
                .spawnAIPlayer(source.getWorld(), pos.getX(), pos.getY(), pos.getZ(), name, behavior.name());
            
            if (aiPlayer != null) {
                if (source.getEntity() instanceof ServerPlayerEntity player) {
                    aiPlayer.setOwnerId(player.getUuid());
                }
                source.sendFeedback(() -> Text.literal("成功在 " + pos + " 生成AI玩家: " + name), true);
                return 1;
            } else {
                source.sendError(Text.literal("生成AI玩家失败"));
                return 0;
            }
        } catch (IllegalArgumentException e) {
            source.sendError(Text.literal("无效的行为类型: " + behaviorStr));
            return 0;
        }
    }
    
    /**
     * 移除AI玩家
     */
    private static int removeAIPlayer(CommandContext<ServerCommandSource> context) throws CommandSyntaxException {
        var entity = EntityArgumentType.getEntity(context, "target");
        
        if (entity instanceof AIPlayerEntity aiPlayer) {
            String name = aiPlayer.getAIName();
            aiPlayer.discard();
            context.getSource().sendFeedback(() -> Text.literal("已移除AI玩家: " + name), true);
            return 1;
        } else {
            context.getSource().sendError(Text.literal("目标不是AI玩家"));
            return 0;
        }
    }
    
    /**
     * 设置AI玩家行为
     */
    private static int setBehavior(CommandContext<ServerCommandSource> context) throws CommandSyntaxException {
        var entity = EntityArgumentType.getEntity(context, "target");
        String behaviorStr = StringArgumentType.getString(context, "behavior");
        
        if (entity instanceof AIPlayerEntity aiPlayer) {
            try {
                AIBehaviorType behavior = AIBehaviorType.valueOf(behaviorStr.toUpperCase());
                aiPlayer.setBehaviorType(behavior);
                context.getSource().sendFeedback(() -> 
                    Text.literal("已设置 " + aiPlayer.getAIName() + " 的行为为: " + behavior.getDisplayName()), true);
                return 1;
            } catch (IllegalArgumentException e) {
                context.getSource().sendError(Text.literal("无效的行为类型: " + behaviorStr));
                return 0;
            }
        } else {
            context.getSource().sendError(Text.literal("目标不是AI玩家"));
            return 0;
        }
    }
    
    /**
     * 让AI玩家前往指定位置
     */
    private static int gotoPosition(CommandContext<ServerCommandSource> context) throws CommandSyntaxException {
        var entity = EntityArgumentType.getEntity(context, "target");
        BlockPos pos = BlockPosArgumentType.getBlockPos(context, "pos");
        
        if (entity instanceof AIPlayerEntity aiPlayer) {
            aiPlayer.setTarget(pos);
            context.getSource().sendFeedback(() -> 
                Text.literal(aiPlayer.getAIName() + " 正在前往 " + pos), true);
            return 1;
        } else {
            context.getSource().sendError(Text.literal("目标不是AI玩家"));
            return 0;
        }
    }
    
    /**
     * 让AI玩家执行动作
     */
    private static int performAction(CommandContext<ServerCommandSource> context) throws CommandSyntaxException {
        var entity = EntityArgumentType.getEntity(context, "target");
        String action = StringArgumentType.getString(context, "action");
        BlockPos pos = BlockPosArgumentType.getBlockPos(context, "pos");
        
        if (entity instanceof AIPlayerEntity aiPlayer) {
            aiPlayer.performActionAt(pos, action);
            context.getSource().sendFeedback(() -> 
                Text.literal(aiPlayer.getAIName() + " 正在执行动作: " + action), true);
            return 1;
        } else {
            context.getSource().sendError(Text.literal("目标不是AI玩家"));
            return 0;
        }
    }
    
    /**
     * 暂停AI玩家
     */
    private static int pauseAIPlayer(CommandContext<ServerCommandSource> context) throws CommandSyntaxException {
        var entity = EntityArgumentType.getEntity(context, "target");
        
        if (entity instanceof AIPlayerEntity aiPlayer) {
            aiPlayer.setActive(false);
            context.getSource().sendFeedback(() -> 
                Text.literal("已暂停AI玩家: " + aiPlayer.getAIName()), true);
            return 1;
        } else {
            context.getSource().sendError(Text.literal("目标不是AI玩家"));
            return 0;
        }
    }
    
    /**
     * 恢复AI玩家
     */
    private static int resumeAIPlayer(CommandContext<ServerCommandSource> context) throws CommandSyntaxException {
        var entity = EntityArgumentType.getEntity(context, "target");
        
        if (entity instanceof AIPlayerEntity aiPlayer) {
            aiPlayer.setActive(true);
            context.getSource().sendFeedback(() -> 
                Text.literal("已恢复AI玩家: " + aiPlayer.getAIName()), true);
            return 1;
        } else {
            context.getSource().sendError(Text.literal("目标不是AI玩家"));
            return 0;
        }
    }
    
    /**
     * 列出所有AI玩家
     */
    private static int listAIPlayers(CommandContext<ServerCommandSource> context) {
        var aiPlayers = MinecraftAIPlayerMod.getInstance().getAIPlayerManager().getAllAIPlayers();
        
        if (aiPlayers.isEmpty()) {
            context.getSource().sendFeedback(() -> Text.literal("当前没有AI玩家"), false);
        } else {
            context.getSource().sendFeedback(() -> Text.literal("AI玩家列表 (" + aiPlayers.size() + "个):"), false);
            for (AIPlayerEntity aiPlayer : aiPlayers) {
                context.getSource().sendFeedback(() -> Text.literal("- " + aiPlayer.getAIName() + 
                    " (行为: " + aiPlayer.getBehaviorType().getDisplayName() + 
                    ", 状态: " + (aiPlayer.isActive() ? "活跃" : "暂停") + ")"), false);
            }
        }
        return 1;
    }
    
    /**
     * 显示AI玩家信息
     */
    private static int showAIPlayerInfo(CommandContext<ServerCommandSource> context) throws CommandSyntaxException {
        var entity = EntityArgumentType.getEntity(context, "target");
        
        if (entity instanceof AIPlayerEntity aiPlayer) {
            context.getSource().sendFeedback(() -> Text.literal("=== AI玩家信息 ==="), false);
            context.getSource().sendFeedback(() -> Text.literal("名称: " + aiPlayer.getAIName()), false);
            context.getSource().sendFeedback(() -> Text.literal("行为: " + aiPlayer.getBehaviorType().getDisplayName()), false);
            context.getSource().sendFeedback(() -> Text.literal("状态: " + (aiPlayer.isActive() ? "活跃" : "暂停")), false);
            context.getSource().sendFeedback(() -> Text.literal("生命值: " + aiPlayer.getHealth() + "/" + aiPlayer.getMaxHealth()), false);
            context.getSource().sendFeedback(() -> Text.literal("位置: " + aiPlayer.getBlockPos()), false);
            
            BlockPos target = aiPlayer.getCurrentTarget();
            if (target != null) {
                context.getSource().sendFeedback(() -> Text.literal("目标: " + target), false);
            }
            
            return 1;
        } else {
            context.getSource().sendError(Text.literal("目标不是AI玩家"));
            return 0;
        }
    }
}
