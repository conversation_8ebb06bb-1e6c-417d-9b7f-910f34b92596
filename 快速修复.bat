@echo off
chcp 65001 >nul
echo ========================================
echo Minecraft AI Player 模组快速修复脚本
echo ========================================
echo.

echo 步骤1: 检查环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Java，请确保Java 17+已安装
    pause
    exit /b 1
)
echo ✓ Java环境正常

echo.
echo 步骤2: 清理构建缓存...
if exist ".gradle" (
    echo 删除 .gradle 文件夹...
    rmdir /s /q ".gradle" 2>nul
)
if exist "build" (
    echo 删除 build 文件夹...
    rmdir /s /q "build" 2>nul
)
echo ✓ 缓存清理完成

echo.
echo 步骤3: 重新生成Gradle Wrapper...
if exist "gradlew.bat" (
    echo 使用现有的Gradle Wrapper
) else (
    echo ❌ 错误: 未找到gradlew.bat
    pause
    exit /b 1
)

echo.
echo 步骤4: 重新生成Minecraft映射...
echo 这可能需要几分钟时间，请耐心等待...
call gradlew.bat genSources --no-daemon --stacktrace
if %errorlevel% neq 0 (
    echo ❌ 映射生成失败，尝试继续...
)

echo.
echo 步骤5: 编译Java代码...
call gradlew.bat compileJava --no-daemon --stacktrace
if %errorlevel% neq 0 (
    echo ❌ 编译失败，请检查错误信息
    echo.
    echo 常见解决方案:
    echo 1. 确保Java版本为17或更高
    echo 2. 检查网络连接（需要下载依赖）
    echo 3. 重启IDE并重新导入项目
    echo 4. 删除 .idea 文件夹（如果使用IntelliJ IDEA）
    pause
    exit /b 1
)
echo ✓ 编译成功

echo.
echo 步骤6: 运行测试...
call gradlew.bat test --no-daemon
if %errorlevel% neq 0 (
    echo ⚠️ 测试失败，但这可能是正常的
)

echo.
echo 步骤7: 完整构建...
call gradlew.bat build --no-daemon
if %errorlevel% neq 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✓ 修复完成！
echo ========================================
echo.
echo 如果IDE仍然显示错误，请：
echo 1. 重启IDE
echo 2. 重新导入项目
echo 3. 等待索引完成
echo.
echo 构建产物位置: build\libs\
echo.
pause
