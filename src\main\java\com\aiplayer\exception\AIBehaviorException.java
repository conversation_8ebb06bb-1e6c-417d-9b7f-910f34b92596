package com.aiplayer.exception;

/**
 * AI行为异常类
 * 当AI行为执行出错时抛出
 */
public class AIBehaviorException extends AIPlayerException {
    
    private final String behaviorType;
    
    public AIBehaviorException(String aiPlayerName, String behaviorType, String message) {
        super(aiPlayerName, "行为执行", message);
        this.behaviorType = behaviorType;
    }
    
    public AIBehaviorException(String aiPlayerName, String behaviorType, String message, Throwable cause) {
        super(aiPlayerName, "行为执行", message, cause);
        this.behaviorType = behaviorType;
    }
    
    public String getBehaviorType() {
        return behaviorType;
    }
}
