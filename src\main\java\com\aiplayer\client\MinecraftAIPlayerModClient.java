package com.aiplayer.client;

import com.aiplayer.MinecraftAIPlayerMod;
import com.aiplayer.client.gui.AIPlayerControlScreen;
import com.aiplayer.client.render.AIPlayerEntityRenderer;
import com.aiplayer.entity.ModEntities;
import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.fabricmc.fabric.api.client.rendering.v1.EntityRendererRegistry;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import org.lwjgl.glfw.GLFW;

/**
 * 客户端模组入口类
 * 处理客户端相关的初始化和事件
 */
public class MinecraftAIPlayerModClient implements ClientModInitializer {
    
    // 按键绑定
    private static KeyBinding openControlPanelKey;
    
    @Override
    public void onInitializeClient() {
        MinecraftAIPlayerMod.LOGGER.info("初始化AI玩家模组客户端...");
        
        // 注册实体渲染器
        registerEntityRenderers();
        
        // 注册按键绑定
        registerKeyBindings();
        
        // 注册客户端事件
        registerClientEvents();
        
        MinecraftAIPlayerMod.LOGGER.info("AI玩家模组客户端初始化完成！");
    }
    
    /**
     * 注册实体渲染器
     */
    private void registerEntityRenderers() {
        EntityRendererRegistry.register(ModEntities.AI_PLAYER, AIPlayerEntityRenderer::new);
    }
    
    /**
     * 注册按键绑定
     */
    private void registerKeyBindings() {
        openControlPanelKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
            "key.minecraft-ai-player.open_control_panel",
            InputUtil.Type.KEYSYM,
            GLFW.GLFW_KEY_G,
            "category.minecraft-ai-player.general"
        ));
    }
    
    /**
     * 注册客户端事件
     */
    private void registerClientEvents() {
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            // 处理按键事件
            while (openControlPanelKey.wasPressed()) {
                if (client.player != null) {
                    client.setScreen(new AIPlayerControlScreen());
                }
            }
        });
    }
    
    /**
     * 获取控制面板按键
     */
    public static KeyBinding getOpenControlPanelKey() {
        return openControlPanelKey;
    }
}
