package com.aiplayer.ai.behavior;

import com.aiplayer.entity.AIPlayerEntity;
import net.minecraft.nbt.NbtCompound;
import net.minecraft.util.math.BlockPos;
import org.jetbrains.annotations.Nullable;

/**
 * AI行为基类
 * 所有AI行为的抽象基类，定义了行为的基本接口
 */
public abstract class AIBehavior {
    
    protected final AIPlayerEntity aiPlayer;
    protected BlockPos targetPosition;
    protected boolean isActive;
    protected long startTime;
    protected long lastActionTime;
    
    public AIBehavior(AIPlayerEntity aiPlayer) {
        this.aiPlayer = aiPlayer;
        this.isActive = false;
        this.startTime = 0;
        this.lastActionTime = 0;
    }
    
    /**
     * 开始执行行为
     */
    public void start() {
        this.isActive = true;
        this.startTime = aiPlayer.getWorld().getTime();
        this.lastActionTime = startTime;
        onStart();
    }
    
    /**
     * 停止执行行为
     */
    public void stop() {
        this.isActive = false;
        onStop();
    }
    
    /**
     * 每tick调用的更新方法
     */
    public void tick() {
        if (!isActive) {
            return;
        }
        
        // 检查是否应该继续执行
        if (!shouldContinue()) {
            stop();
            return;
        }
        
        // 执行行为逻辑
        onTick();
        
        // 更新最后动作时间
        lastActionTime = aiPlayer.getWorld().getTime();
    }
    
    /**
     * 设置目标位置
     */
    public void setTarget(@Nullable BlockPos target) {
        this.targetPosition = target;
        onTargetChanged(target);
    }
    
    /**
     * 获取目标位置
     */
    @Nullable
    public BlockPos getTarget() {
        return targetPosition;
    }
    
    /**
     * 在指定位置执行动作
     */
    public void performActionAt(BlockPos pos, String action) {
        onActionRequested(pos, action);
    }
    
    /**
     * 检查是否应该继续执行行为
     */
    protected boolean shouldContinue() {
        // 基本检查：AI玩家是否存活且激活
        if (!aiPlayer.isAlive() || !aiPlayer.isActive()) {
            return false;
        }
        
        // 子类可以重写此方法添加特定条件
        return true;
    }
    
    /**
     * 获取行为类型
     */
    public abstract AIBehaviorType getBehaviorType();
    
    /**
     * 获取行为优先级
     */
    public int getPriority() {
        return getBehaviorType().getPriority();
    }
    
    /**
     * 检查是否正在执行
     */
    public boolean isActive() {
        return isActive;
    }
    
    /**
     * 获取执行时长（tick）
     */
    public long getExecutionTime() {
        if (!isActive) {
            return 0;
        }
        return aiPlayer.getWorld().getTime() - startTime;
    }
    
    /**
     * 获取距离上次动作的时间（tick）
     */
    public long getTimeSinceLastAction() {
        return aiPlayer.getWorld().getTime() - lastActionTime;
    }
    
    /**
     * 保存行为数据到NBT
     */
    public void writeToNbt(NbtCompound nbt) {
        nbt.putBoolean("IsActive", isActive);
        nbt.putLong("StartTime", startTime);
        nbt.putLong("LastActionTime", lastActionTime);
        
        if (targetPosition != null) {
            nbt.putLong("TargetPosition", targetPosition.asLong());
        }
        
        // 子类可以重写此方法保存额外数据
        writeCustomDataToNbt(nbt);
    }
    
    /**
     * 从NBT读取行为数据
     */
    public void readFromNbt(NbtCompound nbt) {
        isActive = nbt.getBoolean("IsActive");
        startTime = nbt.getLong("StartTime");
        lastActionTime = nbt.getLong("LastActionTime");
        
        if (nbt.contains("TargetPosition")) {
            targetPosition = BlockPos.fromLong(nbt.getLong("TargetPosition"));
        }
        
        // 子类可以重写此方法读取额外数据
        readCustomDataFromNbt(nbt);
    }
    
    // 抽象方法，子类必须实现
    
    /**
     * 行为开始时调用
     */
    protected abstract void onStart();
    
    /**
     * 行为停止时调用
     */
    protected abstract void onStop();
    
    /**
     * 每tick执行的行为逻辑
     */
    protected abstract void onTick();
    
    /**
     * 目标改变时调用
     */
    protected abstract void onTargetChanged(@Nullable BlockPos newTarget);
    
    /**
     * 收到动作请求时调用
     */
    protected abstract void onActionRequested(BlockPos pos, String action);
    
    // 可选重写的方法
    
    /**
     * 保存自定义数据到NBT
     */
    protected void writeCustomDataToNbt(NbtCompound nbt) {
        // 子类可以重写此方法
    }
    
    /**
     * 从NBT读取自定义数据
     */
    protected void readCustomDataFromNbt(NbtCompound nbt) {
        // 子类可以重写此方法
    }
    
    // 工具方法
    
    /**
     * 检查AI玩家是否在目标位置附近
     */
    protected boolean isNearTarget(double distance) {
        if (targetPosition == null) {
            return false;
        }
        
        return aiPlayer.getBlockPos().getSquaredDistance(targetPosition) <= distance * distance;
    }
    
    /**
     * 让AI玩家移动到目标位置
     */
    protected void moveToTarget() {
        if (targetPosition != null) {
            aiPlayer.getNavigation().startMovingTo(
                targetPosition.getX() + 0.5,
                targetPosition.getY(),
                targetPosition.getZ() + 0.5,
                1.0
            );
        }
    }
    
    /**
     * 停止移动
     */
    protected void stopMoving() {
        aiPlayer.getNavigation().stop();
    }
}
