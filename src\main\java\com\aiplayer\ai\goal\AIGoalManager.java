package com.aiplayer.ai.goal;

import com.aiplayer.entity.AIPlayerEntity;
import net.minecraft.entity.ai.goal.*;

/**
 * AI目标管理器
 * 管理AI玩家的基本AI目标（移动、寻路等）
 */
public class AIGoalManager {
    
    private final AIPlayerEntity aiPlayer;
    
    public AIGoalManager(AIPlayerEntity aiPlayer) {
        this.aiPlayer = aiPlayer;
    }
    
    /**
     * 初始化AI目标
     */
    public void initializeGoals() {
        // 添加基本的移动目标
        aiPlayer.goalSelector.add(0, new SwimGoal(aiPlayer));
        aiPlayer.goalSelector.add(1, new LookAroundGoal(aiPlayer));
        aiPlayer.goalSelector.add(2, new WanderAroundGoal(aiPlayer, 1.0));
        
        // 添加基本的目标选择
        aiPlayer.targetSelector.add(1, new RevengeGoal(aiPlayer));
    }
}
