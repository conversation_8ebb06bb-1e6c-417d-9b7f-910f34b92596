package com.aiplayer.util;

import com.aiplayer.exception.AIPlayerException;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 错误处理工具类
 * 提供统一的错误处理和恢复机制
 */
public class ErrorHandler {
    
    // 错误计数器
    private static final ConcurrentHashMap<String, AtomicInteger> errorCounts = new ConcurrentHashMap<>();
    private static final int MAX_ERRORS_PER_AI = 10; // 每个AI玩家最大错误次数
    private static final long ERROR_RESET_INTERVAL = 300000; // 5分钟重置错误计数
    
    // 最后重置时间
    private static long lastResetTime = System.currentTimeMillis();
    
    /**
     * 处理AI玩家异常
     */
    public static void handleAIPlayerException(String aiPlayerName, AIPlayerException exception) {
        // 记录错误
        AIPlayerLogger.logAIError(aiPlayerName, exception.getOperation(), exception);
        
        // 增加错误计数
        AtomicInteger count = errorCounts.computeIfAbsent(aiPlayerName, k -> new AtomicInteger(0));
        int errorCount = count.incrementAndGet();
        
        // 检查是否需要采取措施
        if (errorCount >= MAX_ERRORS_PER_AI) {
            handleCriticalError(aiPlayerName, errorCount);
        }
        
        // 定期重置错误计数
        checkAndResetErrorCounts();
    }
    
    /**
     * 处理一般异常
     */
    public static void handleException(String operation, Throwable exception) {
        AIPlayerLogger.error("执行 {} 时出错", exception, operation);
    }
    
    /**
     * 安全执行操作
     */
    public static <T> T safeExecute(String operation, SafeOperation<T> op, T defaultValue) {
        try {
            return op.execute();
        } catch (Exception e) {
            handleException(operation, e);
            return defaultValue;
        }
    }
    
    /**
     * 安全执行操作（无返回值）
     */
    public static void safeExecute(String operation, SafeVoidOperation op) {
        try {
            op.execute();
        } catch (Exception e) {
            handleException(operation, e);
        }
    }
    
    /**
     * 处理严重错误
     */
    private static void handleCriticalError(String aiPlayerName, int errorCount) {
        AIPlayerLogger.error("AI玩家 {} 错误次数过多 ({}次)，可能需要人工干预", aiPlayerName, errorCount);
        
        // 这里可以添加自动恢复措施，例如：
        // 1. 重置AI玩家状态
        // 2. 切换到安全模式
        // 3. 通知管理员
    }
    
    /**
     * 检查并重置错误计数
     */
    private static void checkAndResetErrorCounts() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastResetTime > ERROR_RESET_INTERVAL) {
            errorCounts.clear();
            lastResetTime = currentTime;
            AIPlayerLogger.info("已重置错误计数器");
        }
    }
    
    /**
     * 向玩家发送错误消息
     */
    public static void sendErrorToPlayer(ServerPlayerEntity player, String message) {
        if (player != null) {
            player.sendMessage(Text.literal("§c[AI玩家错误] " + message), false);
        }
    }
    
    /**
     * 向玩家发送警告消息
     */
    public static void sendWarningToPlayer(ServerPlayerEntity player, String message) {
        if (player != null) {
            player.sendMessage(Text.literal("§e[AI玩家警告] " + message), false);
        }
    }
    
    /**
     * 获取AI玩家的错误次数
     */
    public static int getErrorCount(String aiPlayerName) {
        AtomicInteger count = errorCounts.get(aiPlayerName);
        return count != null ? count.get() : 0;
    }
    
    /**
     * 重置特定AI玩家的错误计数
     */
    public static void resetErrorCount(String aiPlayerName) {
        errorCounts.remove(aiPlayerName);
        AIPlayerLogger.info("已重置AI玩家 {} 的错误计数", aiPlayerName);
    }
    
    /**
     * 安全操作接口
     */
    @FunctionalInterface
    public interface SafeOperation<T> {
        T execute() throws Exception;
    }
    
    /**
     * 安全操作接口（无返回值）
     */
    @FunctionalInterface
    public interface SafeVoidOperation {
        void execute() throws Exception;
    }
}
