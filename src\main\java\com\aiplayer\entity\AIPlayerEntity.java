package com.aiplayer.entity;

import com.aiplayer.ai.AIBehaviorManager;
import com.aiplayer.ai.behavior.AIBehaviorType;
import com.aiplayer.ai.goal.AIGoalManager;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.ai.goal.GoalSelector;
import net.minecraft.entity.attribute.DefaultAttributeContainer;
import net.minecraft.entity.attribute.EntityAttributes;
import net.minecraft.entity.damage.DamageSource;
import net.minecraft.entity.data.DataTracker;
import net.minecraft.entity.data.TrackedData;
import net.minecraft.entity.data.TrackedDataHandlerRegistry;
import net.minecraft.entity.mob.PathAwareEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.nbt.NbtCompound;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.text.Text;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.World;
import org.jetbrains.annotations.Nullable;

import java.util.UUID;

/**
 * AI玩家实体类
 * 继承自PathAwareEntity，具有智能行为和决策能力
 */
public class AIPlayerEntity extends PathAwareEntity {
    
    // 数据追踪器
    private static final TrackedData<String> AI_NAME = DataTracker.registerData(
        AIPlayerEntity.class, TrackedDataHandlerRegistry.STRING);
    private static final TrackedData<String> BEHAVIOR_TYPE = DataTracker.registerData(
        AIPlayerEntity.class, TrackedDataHandlerRegistry.STRING);
    private static final TrackedData<Boolean> IS_ACTIVE = DataTracker.registerData(
        AIPlayerEntity.class, TrackedDataHandlerRegistry.BOOLEAN);
    
    // AI系统组件
    private AIBehaviorManager behaviorManager;
    private AIGoalManager goalManager;
    
    // AI状态
    private UUID ownerId; // 创建者ID
    private long lastDecisionTime;
    private int ticksSinceLastAction;
    
    public AIPlayerEntity(EntityType<? extends AIPlayerEntity> entityType, World world) {
        super(entityType, world);
        
        // 初始化AI组件
        this.behaviorManager = new AIBehaviorManager(this);
        this.goalManager = new AIGoalManager(this);
        
        // 设置基本属性
        this.setCustomNameVisible(true);
        this.setPersistent(true);
        
        // 初始化AI目标
        initializeAIGoals();
    }
    
    /**
     * 创建AI玩家属性
     */
    public static DefaultAttributeContainer.Builder createAIPlayerAttributes() {
        return LivingEntity.createLivingAttributes()
            .add(EntityAttributes.GENERIC_MAX_HEALTH, 20.0)
            .add(EntityAttributes.GENERIC_MOVEMENT_SPEED, 0.25)
            .add(EntityAttributes.GENERIC_ATTACK_DAMAGE, 2.0)
            .add(EntityAttributes.GENERIC_ARMOR, 0.0)
            .add(EntityAttributes.GENERIC_FOLLOW_RANGE, 32.0)
            .add(EntityAttributes.GENERIC_KNOCKBACK_RESISTANCE, 0.0);
    }
    
    @Override
    protected void initDataTracker() {
        super.initDataTracker();
        this.dataTracker.startTracking(AI_NAME, "AI_Player");
        this.dataTracker.startTracking(BEHAVIOR_TYPE, AIBehaviorType.IDLE.name());
        this.dataTracker.startTracking(IS_ACTIVE, true);
    }
    
    /**
     * 初始化AI目标
     */
    private void initializeAIGoals() {
        // 清除默认目标
        this.goalSelector = new GoalSelector(this.getWorld().getProfiler());
        this.targetSelector = new GoalSelector(this.getWorld().getProfiler());
        
        // 添加AI目标
        goalManager.initializeGoals();
    }
    
    @Override
    public void tick() {
        super.tick();
        
        if (!this.getWorld().isClient && isActive()) {
            // 更新AI行为
            behaviorManager.tick();
            
            // 更新决策
            updateDecisionMaking();
            
            // 更新统计
            ticksSinceLastAction++;
        }
    }
    
    /**
     * 更新决策制定
     */
    private void updateDecisionMaking() {
        long currentTime = this.getWorld().getTime();
        
        // 每秒进行一次决策
        if (currentTime - lastDecisionTime >= 20) {
            behaviorManager.makeDecision();
            lastDecisionTime = currentTime;
        }
    }
    
    @Override
    public boolean damage(DamageSource source, float amount) {
        // 如果设置为无敌，则不受伤害
        if (behaviorManager.isInvulnerable()) {
            return false;
        }
        
        // 处理伤害并触发战斗行为
        boolean damaged = super.damage(source, amount);
        if (damaged && source.getAttacker() instanceof LivingEntity attacker) {
            behaviorManager.onAttacked(attacker);
        }
        
        return damaged;
    }
    
    @Override
    public void writeCustomDataToNbt(NbtCompound nbt) {
        super.writeCustomDataToNbt(nbt);
        
        nbt.putString("AIName", getAIName());
        nbt.putString("BehaviorType", getBehaviorType().name());
        nbt.putBoolean("IsActive", isActive());
        
        if (ownerId != null) {
            nbt.putUuid("OwnerId", ownerId);
        }
        
        // 保存AI状态
        behaviorManager.writeToNbt(nbt);
    }
    
    @Override
    public void readCustomDataFromNbt(NbtCompound nbt) {
        super.readCustomDataFromNbt(nbt);
        
        setAIName(nbt.getString("AIName"));
        setBehaviorType(AIBehaviorType.valueOf(nbt.getString("BehaviorType")));
        setActive(nbt.getBoolean("IsActive"));
        
        if (nbt.containsUuid("OwnerId")) {
            ownerId = nbt.getUuid("OwnerId");
        }
        
        // 读取AI状态
        behaviorManager.readFromNbt(nbt);
    }
    
    // Getter和Setter方法
    public String getAIName() {
        return this.dataTracker.get(AI_NAME);
    }
    
    public void setAIName(String name) {
        this.dataTracker.set(AI_NAME, name);
        this.setCustomName(Text.literal(name));
    }
    
    public AIBehaviorType getBehaviorType() {
        return AIBehaviorType.valueOf(this.dataTracker.get(BEHAVIOR_TYPE));
    }
    
    public void setBehaviorType(AIBehaviorType type) {
        this.dataTracker.set(BEHAVIOR_TYPE, type.name());
        behaviorManager.setBehaviorType(type);
    }
    
    public boolean isActive() {
        return this.dataTracker.get(IS_ACTIVE);
    }
    
    public void setActive(boolean active) {
        this.dataTracker.set(IS_ACTIVE, active);
    }
    
    public UUID getOwnerId() {
        return ownerId;
    }
    
    public void setOwnerId(UUID ownerId) {
        this.ownerId = ownerId;
    }
    
    @Nullable
    public PlayerEntity getOwner() {
        if (ownerId != null && this.getWorld() instanceof ServerWorld serverWorld) {
            return serverWorld.getPlayerByUuid(ownerId);
        }
        return null;
    }
    
    public AIBehaviorManager getBehaviorManager() {
        return behaviorManager;
    }
    
    public AIGoalManager getGoalManager() {
        return goalManager;
    }
    
    /**
     * 执行指定位置的动作
     */
    public void performActionAt(BlockPos pos, String action) {
        behaviorManager.performActionAt(pos, action);
        ticksSinceLastAction = 0;
    }
    
    /**
     * 获取当前目标位置
     */
    @Nullable
    public BlockPos getCurrentTarget() {
        return behaviorManager.getCurrentTarget();
    }
    
    /**
     * 设置新的目标位置
     */
    public void setTarget(BlockPos target) {
        behaviorManager.setTarget(target);
    }
}
