package com.aiplayer.ai.behavior.impl;

import com.aiplayer.ai.behavior.AIBehavior;
import com.aiplayer.ai.behavior.AIBehaviorType;
import com.aiplayer.entity.AIPlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.random.Random;
import org.jetbrains.annotations.Nullable;

/**
 * 空闲行为实现
 * AI玩家在没有特定任务时的默认行为
 */
public class IdleBehavior extends AIBehavior {
    
    private static final int RANDOM_WALK_INTERVAL = 200; // 10秒
    private static final int RANDOM_WALK_RANGE = 8;
    
    private BlockPos homePosition;
    private long lastRandomWalkTime;
    private boolean isWandering;
    
    public IdleBehavior(AIPlayerEntity aiPlayer) {
        super(aiPlayer);
        this.homePosition = aiPlayer.getBlockPos();
        this.lastRandomWalkTime = 0;
        this.isWandering = false;
    }
    
    @Override
    public AIBehaviorType getBehaviorType() {
        return AIBehaviorType.IDLE;
    }
    
    @Override
    protected void onStart() {
        // 记录当前位置作为家的位置
        homePosition = aiPlayer.getBlockPos();
        isWandering = false;
        stopMoving();
    }
    
    @Override
    protected void onStop() {
        stopMoving();
        isWandering = false;
    }
    
    @Override
    protected void onTick() {
        long currentTime = aiPlayer.getWorld().getTime();
        
        // 如果正在移动到目标位置，等待到达
        if (targetPosition != null) {
            if (isNearTarget(2.0)) {
                targetPosition = null;
                stopMoving();
                isWandering = false;
            }
            return;
        }
        
        // 定期进行随机漫步
        if (currentTime - lastRandomWalkTime > RANDOM_WALK_INTERVAL) {
            performRandomWalk();
            lastRandomWalkTime = currentTime;
        }
        
        // 如果正在漫步但没有在移动，停止漫步
        if (isWandering && !aiPlayer.getNavigation().isFollowingPath()) {
            isWandering = false;
        }
    }
    
    @Override
    protected void onTargetChanged(@Nullable BlockPos newTarget) {
        if (newTarget != null) {
            moveToTarget();
            isWandering = true;
        } else {
            stopMoving();
            isWandering = false;
        }
    }
    
    @Override
    protected void onActionRequested(BlockPos pos, String action) {
        switch (action.toLowerCase()) {
            case "goto":
                setTarget(pos);
                break;
            case "home":
                setTarget(homePosition);
                break;
            case "stay":
                setTarget(null);
                stopMoving();
                break;
            default:
                // 忽略未知动作
                break;
        }
    }
    
    /**
     * 执行随机漫步
     */
    private void performRandomWalk() {
        if (isWandering) {
            return; // 已经在漫步中
        }
        
        Random random = aiPlayer.getWorld().getRandom();
        
        // 30% 的概率进行随机漫步
        if (random.nextFloat() < 0.3f) {
            // 在家附近随机选择一个位置
            int offsetX = random.nextInt(RANDOM_WALK_RANGE * 2) - RANDOM_WALK_RANGE;
            int offsetZ = random.nextInt(RANDOM_WALK_RANGE * 2) - RANDOM_WALK_RANGE;
            
            BlockPos randomPos = homePosition.add(offsetX, 0, offsetZ);
            
            // 寻找合适的Y坐标
            randomPos = findSafePosition(randomPos);
            
            if (randomPos != null && !randomPos.equals(aiPlayer.getBlockPos())) {
                setTarget(randomPos);
            }
        }
    }
    
    /**
     * 寻找安全的位置
     */
    private BlockPos findSafePosition(BlockPos pos) {
        // 向上搜索，寻找可以站立的位置
        for (int y = pos.getY() - 3; y <= pos.getY() + 3; y++) {
            BlockPos testPos = new BlockPos(pos.getX(), y, pos.getZ());
            
            // 检查是否可以站立（脚下有方块，头部和身体位置为空气）
            if (aiPlayer.getWorld().getBlockState(testPos.down()).isSolidBlock(aiPlayer.getWorld(), testPos.down()) &&
                aiPlayer.getWorld().getBlockState(testPos).isAir() &&
                aiPlayer.getWorld().getBlockState(testPos.up()).isAir()) {
                return testPos;
            }
        }
        
        return null; // 没有找到安全位置
    }
    
    /**
     * 设置家的位置
     */
    public void setHomePosition(BlockPos homePosition) {
        this.homePosition = homePosition;
    }
    
    /**
     * 获取家的位置
     */
    public BlockPos getHomePosition() {
        return homePosition;
    }
    
    /**
     * 检查是否正在漫步
     */
    public boolean isWandering() {
        return isWandering;
    }
    
    @Override
    protected void writeCustomDataToNbt(net.minecraft.nbt.NbtCompound nbt) {
        if (homePosition != null) {
            nbt.putLong("HomePosition", homePosition.asLong());
        }
        nbt.putLong("LastRandomWalkTime", lastRandomWalkTime);
        nbt.putBoolean("IsWandering", isWandering);
    }
    
    @Override
    protected void readCustomDataFromNbt(net.minecraft.nbt.NbtCompound nbt) {
        if (nbt.contains("HomePosition")) {
            homePosition = BlockPos.fromLong(nbt.getLong("HomePosition"));
        }
        lastRandomWalkTime = nbt.getLong("LastRandomWalkTime");
        isWandering = nbt.getBoolean("IsWandering");
    }
}
