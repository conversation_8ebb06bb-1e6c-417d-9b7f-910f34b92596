package com.aiplayer.exception;

/**
 * AI玩家模组基础异常类
 * 所有AI玩家相关异常的基类
 */
public class AIPlayerException extends Exception {
    
    private final String aiPlayerName;
    private final String operation;
    
    public AIPlayerException(String message) {
        super(message);
        this.aiPlayerName = null;
        this.operation = null;
    }
    
    public AIPlayerException(String message, Throwable cause) {
        super(message, cause);
        this.aiPlayerName = null;
        this.operation = null;
    }
    
    public AIPlayerException(String aiPlayerName, String operation, String message) {
        super(formatMessage(aiPlayerName, operation, message));
        this.aiPlayerName = aiPlayerName;
        this.operation = operation;
    }
    
    public AIPlayerException(String aiPlayerName, String operation, String message, Throwable cause) {
        super(formatMessage(aiPlayerName, operation, message), cause);
        this.aiPlayerName = aiPlayerName;
        this.operation = operation;
    }
    
    private static String formatMessage(String aiPlayerName, String operation, String message) {
        return String.format("AI玩家 [%s] 执行 [%s] 时出错: %s", aiPlayerName, operation, message);
    }
    
    public String getAIPlayerName() {
        return aiPlayerName;
    }
    
    public String getOperation() {
        return operation;
    }
}
