package com.aiplayer.client.gui;

import com.aiplayer.ai.behavior.AIBehaviorType;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;

/**
 * AI玩家控制界面
 * 提供图形化界面来控制AI玩家
 */
public class AIPlayerControlScreen extends Screen {
    
    private static final int PANEL_WIDTH = 300;
    private static final int PANEL_HEIGHT = 200;
    
    private TextFieldWidget nameField;
    private ButtonWidget spawnButton;
    private ButtonWidget closeButton;
    private int selectedBehavior = 0;
    
    public AIPlayerControlScreen() {
        super(Text.literal("AI玩家控制面板"));
    }
    
    @Override
    protected void init() {
        int centerX = this.width / 2;
        int centerY = this.height / 2;
        int panelLeft = centerX - PANEL_WIDTH / 2;
        int panelTop = centerY - PANEL_HEIGHT / 2;
        
        // AI玩家名称输入框
        this.nameField = new TextFieldWidget(this.textRenderer, 
            panelLeft + 20, panelTop + 40, 200, 20, Text.literal("AI玩家名称"));
        this.nameField.setPlaceholder(Text.literal("输入AI玩家名称..."));
        this.nameField.setText("AI_Player_" + System.currentTimeMillis() % 1000);
        this.addSelectableChild(this.nameField);
        
        // 生成按钮
        this.spawnButton = ButtonWidget.builder(Text.literal("生成AI玩家"), button -> {
            spawnAIPlayer();
        }).dimensions(panelLeft + 20, panelTop + 100, 100, 20).build();
        this.addDrawableChild(this.spawnButton);
        
        // 行为切换按钮
        ButtonWidget behaviorButton = ButtonWidget.builder(
            Text.literal("行为: " + AIBehaviorType.values()[selectedBehavior].getDisplayName()), 
            button -> {
                selectedBehavior = (selectedBehavior + 1) % AIBehaviorType.values().length;
                button.setMessage(Text.literal("行为: " + AIBehaviorType.values()[selectedBehavior].getDisplayName()));
            }
        ).dimensions(panelLeft + 130, panelTop + 100, 100, 20).build();
        this.addDrawableChild(behaviorButton);
        
        // 关闭按钮
        this.closeButton = ButtonWidget.builder(Text.literal("关闭"), button -> {
            this.close();
        }).dimensions(panelLeft + 20, panelTop + 130, 100, 20).build();
        this.addDrawableChild(this.closeButton);
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // 绘制背景
        this.renderBackground(context, mouseX, mouseY, delta);
        
        int centerX = this.width / 2;
        int centerY = this.height / 2;
        int panelLeft = centerX - PANEL_WIDTH / 2;
        int panelTop = centerY - PANEL_HEIGHT / 2;
        
        // 绘制面板背景
        context.fill(panelLeft, panelTop, panelLeft + PANEL_WIDTH, panelTop + PANEL_HEIGHT, 0x80000000);
        context.drawBorder(panelLeft, panelTop, PANEL_WIDTH, PANEL_HEIGHT, 0xFFFFFFFF);
        
        // 绘制标题
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, centerX, panelTop + 10, 0xFFFFFF);
        
        // 绘制标签
        context.drawTextWithShadow(this.textRenderer, Text.literal("名称:"), panelLeft + 20, panelTop + 30, 0xFFFFFF);
        context.drawTextWithShadow(this.textRenderer, Text.literal("选择行为类型和生成位置"), panelLeft + 20, panelTop + 70, 0xFFFFFF);
        
        // 绘制名称输入框
        this.nameField.render(context, mouseX, mouseY, delta);
        
        // 绘制其他组件
        super.render(context, mouseX, mouseY, delta);
    }
    
    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (this.nameField.keyPressed(keyCode, scanCode, modifiers)) {
            return true;
        }
        return super.keyPressed(keyCode, scanCode, modifiers);
    }
    
    @Override
    public boolean charTyped(char chr, int modifiers) {
        if (this.nameField.charTyped(chr, modifiers)) {
            return true;
        }
        return super.charTyped(chr, modifiers);
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (this.nameField.mouseClicked(mouseX, mouseY, button)) {
            return true;
        }
        return super.mouseClicked(mouseX, mouseY, button);
    }
    
    /**
     * 生成AI玩家
     */
    private void spawnAIPlayer() {
        if (this.client != null && this.client.player != null) {
            String name = this.nameField.getText().trim();
            if (name.isEmpty()) {
                name = "AI_Player";
            }
            
            AIBehaviorType behavior = AIBehaviorType.values()[selectedBehavior];
            
            // 这里应该发送网络数据包到服务器
            // 暂时显示消息
            this.client.player.sendMessage(Text.literal("请求生成AI玩家: " + name + " (行为: " + behavior.getDisplayName() + ")"), false);
            
            this.close();
        }
    }
    
    @Override
    public boolean shouldPause() {
        return false;
    }
}
