package com.aiplayer.test;

import com.aiplayer.ai.behavior.AIBehaviorType;
import com.aiplayer.config.ModConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * AI玩家模组基础测试类
 * 注意：这些是基础的单元测试，实际的Minecraft环境测试需要更复杂的设置
 */
public class AIPlayerTest {
    
    private ModConfig config;
    
    @BeforeEach
    public void setUp() {
        config = new ModConfig();
    }
    
    /**
     * 测试配置文件默认值
     */
    @Test
    public void testDefaultConfig() {
        assertNotNull(config.aiPlayerSettings);
        assertNotNull(config.behaviorSettings);
        assertNotNull(config.performanceSettings);
        
        assertEquals(10, config.aiPlayerSettings.maxAIPlayers);
        assertTrue(config.aiPlayerSettings.enableAI);
        assertEquals("AI_Player", config.aiPlayerSettings.defaultAIName);
        assertEquals(20.0, config.aiPlayerSettings.aiPlayerHealth);
    }
    
    /**
     * 测试行为类型枚举
     */
    @Test
    public void testBehaviorTypes() {
        // 测试所有行为类型都有有效的ID和显示名称
        for (AIBehaviorType type : AIBehaviorType.values()) {
            assertNotNull(type.getId());
            assertNotNull(type.getDisplayName());
            assertFalse(type.getId().isEmpty());
            assertFalse(type.getDisplayName().isEmpty());
        }
        
        // 测试特定行为类型的属性
        assertTrue(AIBehaviorType.MINING.isResourceGatheringBehavior());
        assertTrue(AIBehaviorType.BUILDING.isConstructionBehavior());
        assertTrue(AIBehaviorType.COMBAT.isCombatBehavior());
        assertFalse(AIBehaviorType.IDLE.isActiveBehavior());
    }
    
    /**
     * 测试行为类型查找
     */
    @Test
    public void testBehaviorTypeLookup() {
        assertEquals(AIBehaviorType.MINING, AIBehaviorType.fromId("mining"));
        assertEquals(AIBehaviorType.BUILDING, AIBehaviorType.fromId("building"));
        assertEquals(AIBehaviorType.IDLE, AIBehaviorType.fromId("invalid_id"));
    }
    
    /**
     * 测试配置验证
     */
    @Test
    public void testConfigValidation() {
        // 测试最大AI玩家数量限制
        assertTrue(config.aiPlayerSettings.maxAIPlayers > 0);
        assertTrue(config.aiPlayerSettings.maxAIPlayers <= 100);
        
        // 测试范围设置
        assertTrue(config.behaviorSettings.miningRange > 0);
        assertTrue(config.behaviorSettings.buildingRange > 0);
        assertTrue(config.behaviorSettings.farmingRange > 0);
        assertTrue(config.behaviorSettings.combatRange > 0);
        
        // 测试性能设置
        assertTrue(config.performanceSettings.aiTickInterval > 0);
        assertTrue(config.performanceSettings.pathfindingUpdateInterval > 0);
        assertTrue(config.performanceSettings.maxPathfindingDistance > 0);
        assertTrue(config.performanceSettings.maxConcurrentAIOperations > 0);
    }
    
    /**
     * 测试行为优先级
     */
    @Test
    public void testBehaviorPriorities() {
        // 战斗行为应该有较高优先级
        assertTrue(AIBehaviorType.COMBAT.getPriority() > AIBehaviorType.IDLE.getPriority());
        
        // 守卫行为应该有较高优先级
        assertTrue(AIBehaviorType.GUARDING.getPriority() > AIBehaviorType.COLLECTING.getPriority());
    }
    
    /**
     * 测试配置文件保存和加载
     */
    @Test
    public void testConfigSerialization() {
        // 修改一些配置值
        config.aiPlayerSettings.maxAIPlayers = 5;
        config.behaviorSettings.miningRange = 20;
        config.performanceSettings.enableDebugLogging = true;
        
        // 这里应该测试配置的序列化和反序列化
        // 由于需要文件系统操作，这里只做基本验证
        assertNotNull(config.aiPlayerSettings);
        assertEquals(5, config.aiPlayerSettings.maxAIPlayers);
        assertEquals(20, config.behaviorSettings.miningRange);
        assertTrue(config.performanceSettings.enableDebugLogging);
    }
    
    /**
     * 测试错误处理
     */
    @Test
    public void testErrorHandling() {
        // 测试无效的行为类型
        assertThrows(IllegalArgumentException.class, () -> {
            AIBehaviorType.valueOf("INVALID_BEHAVIOR");
        });
        
        // 测试边界值
        config.aiPlayerSettings.maxAIPlayers = -1;
        // 在实际实现中，应该有验证逻辑来处理这种情况
    }
    
    /**
     * 性能测试示例
     */
    @Test
    public void testPerformance() {
        long startTime = System.currentTimeMillis();
        
        // 模拟创建多个行为类型
        for (int i = 0; i < 1000; i++) {
            for (AIBehaviorType type : AIBehaviorType.values()) {
                type.getId();
                type.getDisplayName();
                type.getPriority();
            }
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 确保操作在合理时间内完成（1秒）
        assertTrue(duration < 1000, "行为类型操作耗时过长: " + duration + "ms");
    }
}
