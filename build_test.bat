@echo off
echo ========================================
echo AI玩家模组构建测试脚本
echo ========================================
echo.

echo 检查Java版本...
java -version
if %errorlevel% neq 0 (
    echo 错误: 未找到Java，请确保Java 17+已安装并在PATH中
    pause
    exit /b 1
)
echo.

echo 检查Gradle...
if exist gradlew.bat (
    echo 找到Gradle Wrapper
) else (
    echo 错误: 未找到gradlew.bat
    pause
    exit /b 1
)
echo.

echo 清理之前的构建...
call gradlew.bat clean
if %errorlevel% neq 0 (
    echo 警告: 清理过程中出现错误
)
echo.

echo 开始构建模组...
call gradlew.bat build
if %errorlevel% neq 0 (
    echo 错误: 构建失败
    echo 请检查上面的错误信息
    pause
    exit /b 1
)
echo.

echo 检查构建结果...
if exist "build\libs\minecraft-ai-player-1.0.0.jar" (
    echo ✓ 主模组文件构建成功
) else (
    echo ✗ 主模组文件未找到
)

if exist "build\libs\minecraft-ai-player-1.0.0-sources.jar" (
    echo ✓ 源代码文件构建成功
) else (
    echo ✗ 源代码文件未找到
)
echo.

echo ========================================
echo 构建测试完成！
echo ========================================
echo.
echo 如果构建成功，你可以在以下位置找到模组文件：
echo build\libs\minecraft-ai-player-1.0.0.jar
echo.
echo 安装方法：
echo 1. 确保已安装Fabric Loader和Fabric API
echo 2. 将jar文件复制到.minecraft\mods文件夹
echo 3. 启动游戏
echo.
pause
