package com.aiplayer.util;

import com.aiplayer.MinecraftAIPlayerMod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * AI玩家专用日志记录器
 * 提供统一的日志记录接口和格式
 */
public class AIPlayerLogger {
    
    private static final Logger LOGGER = LoggerFactory.getLogger("AIPlayer");
    
    // 日志级别
    public enum Level {
        DEBUG, INFO, WARN, ERROR
    }
    
    /**
     * 记录调试信息
     */
    public static void debug(String message, Object... args) {
        if (MinecraftAIPlayerMod.getInstance().getConfig().performanceSettings.enableDebugLogging) {
            LOGGER.debug(formatMessage(message), args);
        }
    }
    
    /**
     * 记录一般信息
     */
    public static void info(String message, Object... args) {
        LOGGER.info(formatMessage(message), args);
    }
    
    /**
     * 记录警告信息
     */
    public static void warn(String message, Object... args) {
        LOGGER.warn(formatMessage(message), args);
    }
    
    /**
     * 记录错误信息
     */
    public static void error(String message, Object... args) {
        LOGGER.error(formatMessage(message), args);
    }
    
    /**
     * 记录错误信息（带异常）
     */
    public static void error(String message, Throwable throwable, Object... args) {
        LOGGER.error(formatMessage(message), args, throwable);
    }
    
    /**
     * 记录AI玩家特定的行为日志
     */
    public static void logAIBehavior(String aiPlayerName, String behavior, String action) {
        debug("AI玩家 [{}] 行为 [{}]: {}", aiPlayerName, behavior, action);
    }
    
    /**
     * 记录AI玩家状态变化
     */
    public static void logAIStateChange(String aiPlayerName, String oldState, String newState) {
        info("AI玩家 [{}] 状态变化: {} -> {}", aiPlayerName, oldState, newState);
    }
    
    /**
     * 记录AI玩家错误
     */
    public static void logAIError(String aiPlayerName, String operation, Throwable error) {
        error("AI玩家 [{}] 执行 [{}] 时出错: {}", aiPlayerName, operation, error.getMessage(), error);
    }
    
    /**
     * 记录性能相关信息
     */
    public static void logPerformance(String operation, long timeMs) {
        if (timeMs > 50) { // 超过50ms的操作记录为警告
            warn("性能警告: {} 耗时 {}ms", operation, timeMs);
        } else {
            debug("性能: {} 耗时 {}ms", operation, timeMs);
        }
    }
    
    /**
     * 记录网络通信
     */
    public static void logNetwork(String direction, String packetType, String details) {
        debug("网络 [{}] {}: {}", direction, packetType, details);
    }
    
    /**
     * 格式化消息
     */
    private static String formatMessage(String message) {
        return "[AI玩家] " + message;
    }
    
    /**
     * 检查是否启用调试日志
     */
    public static boolean isDebugEnabled() {
        return MinecraftAIPlayerMod.getInstance().getConfig().performanceSettings.enableDebugLogging;
    }
}
