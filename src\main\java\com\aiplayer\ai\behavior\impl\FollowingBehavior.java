package com.aiplayer.ai.behavior.impl;

import com.aiplayer.ai.behavior.AIBehavior;
import com.aiplayer.ai.behavior.AIBehaviorType;
import com.aiplayer.entity.AIPlayerEntity;
import net.minecraft.util.math.BlockPos;
import org.jetbrains.annotations.Nullable;

public class Following<PERSON>eh<PERSON>or extends AIBehavior {
    public FollowingBehavior(AIPlayerEntity aiPlayer) { super(aiPlayer); }
    @Override public AIBehaviorType getBehaviorType() { return AIBehaviorType.FOLLOWING; }
    @Override protected void onStart() {}
    @Override protected void onStop() { stopMoving(); }
    @Override protected void onTick() {}
    @Override protected void onTargetChanged(@Nullable BlockPos newTarget) {}
    @Override protected void onActionRequested(BlockPos pos, String action) {}
}
