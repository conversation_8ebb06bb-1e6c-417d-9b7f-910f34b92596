# AI玩家模组开发者指南

## 项目架构

### 核心组件

```
                 # Mixin扩展
```

### 设计模式

1. **状态机模式**: AI行为管理
2. **策略模式**: 不同行为的实现
3. **观察者模式**: 状态变化通知
4. **单例模式**: 模组实例管理
5. **工厂模式**: 实体和行为创建

## 添加新的AI行为

### 步骤1：定义行为类型

在 `AIBehaviorType.java` 中添加新的枚举值：

```java
public enum AIBehaviorType {
    // 现有行为...
    
    /**
     * 自定义行为 - 描述你的行为功能
     */
    CUSTOM("custom", "自定义", 12);
}
```

### 步骤2：创建行为实现

创建新的行为类 `CustomBehavior.java`：

```java
package com.aiplayer.ai.behavior.impl;

import com.aiplayer.ai.behavior.AIBehavior;
import com.aiplayer.ai.behavior.AIBehaviorType;
import com.aiplayer.entity.AIPlayerEntity;
import net.minecraft.util.math.BlockPos;
import org.jetbrains.annotations.Nullable;

public class CustomBehavior extends AIBehavior {
    
    // 行为特定的状态变量
    private int customState;
    private long lastActionTime;
    
    public CustomBehavior(AIPlayerEntity aiPlayer) {
        super(aiPlayer);
        this.customState = 0;
        this.lastActionTime = 0;
    }
    
    @Override
    public AIBehaviorType getBehaviorType() {
        return AIBehaviorType.CUSTOM;
    }
    
    @Override
    protected void onStart() {
        // 行为开始时的初始化逻辑com.aiplayer/
├── MinecraftAIPlayerMod.java          # 主模组类，负责初始化
├── ai/                                # AI系统核心
│   ├── AIPlayerManager.java           # 管理所有AI玩家
│   ├── AIBehaviorManager.java         # 管理单个AI的行为
│   ├── behavior/                      # 行为实现
│   │   ├── AIBehavior.java            # 行为基类
│   │   ├── AIBehaviorType.java        # 行为类型枚举
│   │   └── impl/                      # 具体行为实现
│   ├── state/                         # 状态管理
│   │   ├── AIState.java               # AI状态数据
│   │   └── AIStateManager.java        # 状态管理器
│   └── goal/                          # 目标管理
│       └── AIGoalManager.java         # AI目标管理器
├── entity/                            # 实体相关
│   ├── AIPlayerEntity.java            # AI玩家实体
│   └── ModEntities.java               # 实体注册
├── command/                           # 命令系统
├── config/                            # 配置管理
├── network/                           # 网络通信
├── client/                            # 客户端代码
├── util/                              # 工具类
├── exception/                         # 异常处理
└── mixin/            
        customState = 0;
        lastActionTime = aiPlayer.getWorld().getTime();
        
        // 例如：确保AI有必要的工具
        ensureRequiredItems();
    }
    
    @Override
    protected void onStop() {
        // 行为停止时的清理逻辑
        stopMoving();
        customState = 0;
    }
    
    @Override
    protected void onTick() {
        // 每tick执行的主要逻辑
        long currentTime = aiPlayer.getWorld().getTime();
        
        switch (customState) {
            case 0:
                // 初始状态：寻找目标
                findTarget();
                break;
            case 1:
                // 移动状态：前往目标
                moveToTarget();
                break;
            case 2:
                // 执行状态：执行具体动作
                performAction();
                break;
        }
        
        // 定期更新状态
        if (currentTime - lastActionTime > 100) { // 5秒
            updateState();
            lastActionTime = currentTime;
        }
    }
    
    @Override
    protected void onTargetChanged(@Nullable BlockPos newTarget) {
        if (newTarget != null) {
            customState = 1; // 切换到移动状态
            moveToTarget();
        }
    }
    
    @Override
    protected void onActionRequested(BlockPos pos, String action) {
        switch (action.toLowerCase()) {
            case "custom_action":
                setTarget(pos);
                break;
            case "reset":
                customState = 0;
                break;
            default:
                // 处理未知动作
                break;
        }
    }
    
    // 自定义方法
    private void findTarget() {
        // 实现目标寻找逻辑
        // 例如：寻找特定类型的方块或实体
    }
    
    private void performAction() {
        // 实现具体的动作逻辑
        // 例如：与方块交互、攻击实体等
    }
    
    private void updateState() {
        // 实现状态更新逻辑
        // 根据当前情况决定下一步行为
    }
    
    private void ensureRequiredItems() {
        // 确保AI有执行行为所需的物品
    }
    
    @Override
    protected void writeCustomDataToNbt(net.minecraft.nbt.NbtCompound nbt) {
        nbt.putInt("CustomState", customState);
        nbt.putLong("LastActionTime", lastActionTime);
    }
    
    @Override
    protected void readCustomDataFromNbt(net.minecraft.nbt.NbtCompound nbt) {
        customState = nbt.getInt("CustomState");
        lastActionTime = nbt.getLong("LastActionTime");
    }
}
```

### 步骤3：注册行为

在 `AIBehaviorManager.java` 的 `initializeBehaviors()` 方法中注册新行为：

```java
private void initializeBehaviors() {
    // 现有行为注册...
    
    behaviors.put(AIBehaviorType.CUSTOM, new CustomBehavior(aiPlayer));
}
```

### 步骤4：添加本地化

在语言文件中添加新行为的显示名称：

```json
{
  "behavior.minecraft-ai-player.custom": "自定义行为"
}
```

## 扩展AI决策系统

### 添加新的状态条件

在 `AIState.java` 中添加新的状态检查方法：

```java
public class AIState {
    // 现有代码...
    
    /**
     * 检查自定义条件
     */
    public boolean isCustomConditionMet() {
        // 实现自定义条件检查逻辑
        return false;
    }
}
```

### 修改决策逻辑

在 `AIBehaviorManager.java` 的 `decideBehavior()` 方法中添加新的决策规则：

```java
private AIBehaviorType decideBehavior(AIState currentState) {
    // 现有决策逻辑...
    
    // 添加自定义决策规则
    if (currentState.isCustomConditionMet()) {
        return AIBehaviorType.CUSTOM;
    }
    
    return currentBehaviorType;
}
```

## 添加新的命令

### 创建命令处理方法

在 `AIPlayerCommands.java` 中添加新的命令处理方法：

```java
/**
 * 自定义命令处理
 */
private static int handleCustomCommand(CommandContext<ServerCommandSource> context) throws CommandSyntaxException {
    var entity = EntityArgumentType.getEntity(context, "target");
    String parameter = StringArgumentType.getString(context, "parameter");
    
    if (entity instanceof AIPlayerEntity aiPlayer) {
        // 执行自定义命令逻辑
        aiPlayer.getBehaviorManager().executeCustomCommand(parameter);
        
        context.getSource().sendFeedback(() -> 
            Text.literal("执行自定义命令: " + parameter), true);
        return 1;
    } else {
        context.getSource().sendError(Text.literal("目标不是AI玩家"));
        return 0;
    }
}
```

### 注册命令

在 `register()` 方法中注册新命令：

```java
dispatcher.register(CommandManager.literal("aiplayer")
    // 现有命令...
    .then(CommandManager.literal("custom")
        .then(CommandManager.argument("target", EntityArgumentType.entity())
            .then(CommandManager.argument("parameter", StringArgumentType.string())
                .executes(AIPlayerCommands::handleCustomCommand))))
);
```

## 网络通信扩展

### 添加新的数据包

在 `ModNetworking.java` 中定义新的数据包标识符：

```java
public static final Identifier CUSTOM_PACKET = MinecraftAIPlayerMod.id("custom_packet");
```

### 注册数据包处理器

```java
private static void registerServerPacketHandlers() {
    // 现有处理器...
    
    ServerPlayNetworking.registerGlobalReceiver(CUSTOM_PACKET, 
        (server, player, handler, buf, responseSender) -> {
            handleCustomPacket(player, buf);
        });
}

private static void handleCustomPacket(ServerPlayerEntity player, PacketByteBuf buf) {
    try {
        // 读取数据包内容
        String customData = buf.readString();
        
        // 在服务器线程中处理
        player.getServer().execute(() -> {
            // 处理自定义数据包逻辑
        });
        
    } catch (Exception e) {
        MinecraftAIPlayerMod.LOGGER.error("处理自定义数据包时出错", e);
    }
}
```

## 性能优化建议

### 1. 异步处理

对于耗时操作，使用异步处理：

```java
CompletableFuture.runAsync(() -> {
    // 耗时操作
}).exceptionally(throwable -> {
    AIPlayerLogger.error("异步操作失败", throwable);
    return null;
});
```

### 2. 缓存机制

实现结果缓存以避免重复计算：

```java
private final Map<String, CachedResult> cache = new ConcurrentHashMap<>();

public Result getResult(String key) {
    CachedResult cached = cache.get(key);
    if (cached != null && !cached.isExpired()) {
        return cached.getResult();
    }
    
    Result result = computeResult(key);
    cache.put(key, new CachedResult(result));
    return result;
}
```

### 3. 批量操作

将多个小操作合并为批量操作：

```java
private final List<Operation> pendingOperations = new ArrayList<>();

public void addOperation(Operation op) {
    pendingOperations.add(op);
    
    if (pendingOperations.size() >= BATCH_SIZE) {
        processBatch();
    }
}

private void processBatch() {
    // 批量处理操作
    pendingOperations.clear();
}
```

## 调试和测试

### 启用调试日志

```java
// 在配置文件中启用调试
config.performanceSettings.enableDebugLogging = true;

// 使用调试日志
AIPlayerLogger.debug("AI玩家 {} 执行行为 {}", aiPlayer.getAIName(), behavior);
```

### 单元测试示例

```java
@Test
public void testAIBehaviorSwitching() {
    // 创建测试AI玩家
    AIPlayerEntity testAI = createTestAIPlayer();
    
    // 设置初始行为
    testAI.setBehaviorType(AIBehaviorType.IDLE);
    assertEquals(AIBehaviorType.IDLE, testAI.getBehaviorType());
    
    // 测试行为切换
    testAI.setBehaviorType(AIBehaviorType.MINING);
    assertEquals(AIBehaviorType.MINING, testAI.getBehaviorType());
}
```

## 发布和分发

### 构建模组

```bash
./gradlew build
```

### 生成的文件

- `build/libs/minecraft-ai-player-1.0.0.jar` - 主模组文件
- `build/libs/minecraft-ai-player-1.0.0-sources.jar` - 源代码文件

### 版本管理

在 `gradle.properties` 中更新版本号：

```properties
mod_version=1.1.0
```

## 贡献指南

### 代码规范

1. 使用Java 17特性
2. 遵循驼峰命名法
3. 添加适当的注释和文档
4. 使用SLF4J进行日志记录
5. 处理所有可能的异常

### 提交规范

```
feat: 添加新的AI行为
fix: 修复挖矿行为的bug
docs: 更新开发者文档
style: 代码格式化
refactor: 重构状态管理系统
test: 添加行为切换测试
```

### Pull Request流程

1. Fork仓库
2. 创建功能分支
3. 实现功能并测试
4. 更新文档
5. 提交Pull Request
6. 代码审查
7. 合并到主分支
