package com.aiplayer;

import net.fabricmc.api.ModInitializer;
import net.fabricmc.fabric.api.command.v2.CommandRegistrationCallback;
import net.fabricmc.fabric.api.entity.event.v1.ServerPlayerEvents;
import net.fabricmc.fabric.api.event.lifecycle.v1.ServerLifecycleEvents;
import net.fabricmc.fabric.api.event.lifecycle.v1.ServerTickEvents;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.util.Identifier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.aiplayer.ai.AIPlayerManager;
import com.aiplayer.command.AIPlayerCommands;
import com.aiplayer.config.ModConfig;
import com.aiplayer.entity.ModEntities;
import com.aiplayer.network.ModNetworking;

/**
 * 《我的世界》AI玩家模组主类
 * 
 * 这个模组提供了智能AI玩家功能，包括：
 * - 自动挖矿
 * - 自动建造
 * - 自动战斗
 * - 自动农业
 * - 玩家交互控制
 */
public class MinecraftAIPlayerMod implements ModInitializer {
    
    public static final String MOD_ID = "minecraft-ai-player";
    public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);
    
    private static MinecraftAIPlayerMod instance;
    private AIPlayerManager aiPlayerManager;
    private ModConfig config;
    
    @Override
    public void onInitialize() {
        instance = this;
        
        LOGGER.info("初始化《我的世界》AI玩家模组...");
        
        // 初始化配置
        config = new ModConfig();
        config.load();
        
        // 注册实体类型
        ModEntities.register();
        
        // 初始化网络通信
        ModNetworking.init();
        
        // 初始化AI玩家管理器
        aiPlayerManager = new AIPlayerManager();
        
        // 注册命令
        CommandRegistrationCallback.EVENT.register(AIPlayerCommands::register);
        
        // 注册事件监听器
        registerEventListeners();
        
        LOGGER.info("《我的世界》AI玩家模组初始化完成！");
    }
    
    /**
     * 注册事件监听器
     */
    private void registerEventListeners() {
        // 服务器启动事件
        ServerLifecycleEvents.SERVER_STARTED.register(server -> {
            LOGGER.info("服务器启动，AI玩家系统准备就绪");
            aiPlayerManager.onServerStart(server);
        });
        
        // 服务器停止事件
        ServerLifecycleEvents.SERVER_STOPPING.register(server -> {
            LOGGER.info("服务器停止，保存AI玩家数据");
            aiPlayerManager.onServerStop(server);
        });
        
        // 服务器tick事件
        ServerTickEvents.END_SERVER_TICK.register(server -> {
            aiPlayerManager.tick(server);
        });
        
        // 玩家加入事件
        ServerPlayerEvents.AFTER_RESPAWN.register((oldPlayer, newPlayer, alive) -> {
            aiPlayerManager.onPlayerJoin(newPlayer);
        });
    }
    
    /**
     * 获取模组实例
     */
    public static MinecraftAIPlayerMod getInstance() {
        return instance;
    }
    
    /**
     * 获取AI玩家管理器
     */
    public AIPlayerManager getAIPlayerManager() {
        return aiPlayerManager;
    }
    
    /**
     * 获取模组配置
     */
    public ModConfig getConfig() {
        return config;
    }
    
    /**
     * 创建模组资源标识符
     */
    public static Identifier id(String path) {
        return new Identifier(MOD_ID, path);
    }
}
