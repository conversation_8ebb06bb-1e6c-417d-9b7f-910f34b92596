package com.aiplayer.ai.behavior;

/**
 * AI行为类型枚举
 * 定义AI玩家可以执行的各种行为模式
 */
public enum AIBehaviorType {
    
    /**
     * 空闲状态 - AI玩家不执行任何特定任务
     */
    IDLE("idle", "空闲", 0),
    
    /**
     * 挖矿行为 - 自动寻找和挖掘矿物
     */
    MINING("mining", "挖矿", 1),
    
    /**
     * 建造行为 - 根据蓝图或模式建造结构
     */
    BUILDING("building", "建造", 2),
    
    /**
     * 农业行为 - 种植、收获和管理农作物
     */
    FARMING("farming", "农业", 3),
    
    /**
     * 战斗行为 - 主动寻找和攻击敌对生物
     */
    COMBAT("combat", "战斗", 4),
    
    /**
     * 探索行为 - 探索未知区域并收集资源
     */
    EXPLORATION("exploration", "探索", 5),
    
    /**
     * 收集行为 - 收集掉落物品和资源
     */
    COLLECTING("collecting", "收集", 6),
    
    /**
     * 守卫行为 - 保护指定区域或玩家
     */
    GUARDING("guarding", "守卫", 7),
    
    /**
     * 跟随行为 - 跟随指定玩家
     */
    FOLLOWING("following", "跟随", 8),
    
    /**
     * 交易行为 - 与村民或其他实体进行交易
     */
    TRADING("trading", "交易", 9),
    
    /**
     * 维修行为 - 修复损坏的结构
     */
    REPAIRING("repairing", "维修", 10),
    
    /**
     * 运输行为 - 在指定地点之间运输物品
     */
    TRANSPORTING("transporting", "运输", 11);
    
    private final String id;
    private final String displayName;
    private final int priority;
    
    AIBehaviorType(String id, String displayName, int priority) {
        this.id = id;
        this.displayName = displayName;
        this.priority = priority;
    }
    
    /**
     * 获取行为ID
     */
    public String getId() {
        return id;
    }
    
    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 获取优先级（数值越高优先级越高）
     */
    public int getPriority() {
        return priority;
    }
    
    /**
     * 根据ID获取行为类型
     */
    public static AIBehaviorType fromId(String id) {
        for (AIBehaviorType type : values()) {
            if (type.id.equals(id)) {
                return type;
            }
        }
        return IDLE;
    }
    
    /**
     * 检查是否为主动行为（需要目标）
     */
    public boolean isActiveBehavior() {
        return this != IDLE && this != FOLLOWING;
    }
    
    /**
     * 检查是否为战斗相关行为
     */
    public boolean isCombatBehavior() {
        return this == COMBAT || this == GUARDING;
    }
    
    /**
     * 检查是否为资源收集行为
     */
    public boolean isResourceGatheringBehavior() {
        return this == MINING || this == FARMING || this == COLLECTING;
    }
    
    /**
     * 检查是否为建设相关行为
     */
    public boolean isConstructionBehavior() {
        return this == BUILDING || this == REPAIRING;
    }
}
