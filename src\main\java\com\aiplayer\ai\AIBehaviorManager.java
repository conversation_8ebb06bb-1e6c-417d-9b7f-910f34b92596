package com.aiplayer.ai;

import com.aiplayer.MinecraftAIPlayerMod;
import com.aiplayer.ai.behavior.AIBehaviorType;
import com.aiplayer.ai.behavior.impl.*;
import com.aiplayer.ai.state.AIState;
import com.aiplayer.ai.state.AIStateManager;
import com.aiplayer.entity.AIPlayerEntity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.nbt.NbtCompound;
import net.minecraft.util.math.BlockPos;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * AI行为管理器
 * 负责管理AI玩家的行为状态和决策逻辑
 */
public class AIBehaviorManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(AIBehaviorManager.class);
    
    private final AIPlayerEntity aiPlayer;
    private final AIStateManager stateManager;
    private final Map<AIBehaviorType, AIBehavior> behaviors;
    
    // 当前状态
    private AIBehaviorType currentBehaviorType;
    private AIBehavior currentBehavior;
    private BlockPos currentTarget;
    private boolean isInvulnerable;
    
    // 决策相关
    private long lastDecisionTime;
    private int decisionCooldown;
    
    public AIBehaviorManager(AIPlayerEntity aiPlayer) {
        this.aiPlayer = aiPlayer;
        this.stateManager = new AIStateManager(aiPlayer);
        this.behaviors = new HashMap<>();
        
        // 初始化行为
        initializeBehaviors();
        
        // 设置默认行为
        this.currentBehaviorType = AIBehaviorType.IDLE;
        this.currentBehavior = behaviors.get(AIBehaviorType.IDLE);
        this.isInvulnerable = false;
        this.decisionCooldown = 20; // 1秒
    }
    
    /**
     * 初始化所有行为实现
     */
    private void initializeBehaviors() {
        behaviors.put(AIBehaviorType.IDLE, new IdleBehavior(aiPlayer));
        behaviors.put(AIBehaviorType.MINING, new MiningBehavior(aiPlayer));
        behaviors.put(AIBehaviorType.BUILDING, new BuildingBehavior(aiPlayer));
        behaviors.put(AIBehaviorType.FARMING, new FarmingBehavior(aiPlayer));
        behaviors.put(AIBehaviorType.COMBAT, new CombatBehavior(aiPlayer));
        behaviors.put(AIBehaviorType.EXPLORATION, new ExplorationBehavior(aiPlayer));
        behaviors.put(AIBehaviorType.COLLECTING, new CollectingBehavior(aiPlayer));
        behaviors.put(AIBehaviorType.GUARDING, new GuardingBehavior(aiPlayer));
        behaviors.put(AIBehaviorType.FOLLOWING, new FollowingBehavior(aiPlayer));
        behaviors.put(AIBehaviorType.TRADING, new TradingBehavior(aiPlayer));
        behaviors.put(AIBehaviorType.REPAIRING, new RepairingBehavior(aiPlayer));
        behaviors.put(AIBehaviorType.TRANSPORTING, new TransportingBehavior(aiPlayer));
        
        LOGGER.debug("初始化了 {} 种AI行为", behaviors.size());
    }
    
    /**
     * 每tick调用的更新方法
     */
    public void tick() {
        // 更新状态管理器
        stateManager.tick();
        
        // 执行当前行为
        if (currentBehavior != null && aiPlayer.isActive()) {
            try {
                currentBehavior.tick();
            } catch (Exception e) {
                LOGGER.error("执行AI行为时出错: {}", currentBehaviorType, e);
                // 出错时切换到空闲状态
                setBehaviorType(AIBehaviorType.IDLE);
            }
        }
    }
    
    /**
     * 进行决策
     */
    public void makeDecision() {
        long currentTime = aiPlayer.getWorld().getTime();
        
        // 检查决策冷却
        if (currentTime - lastDecisionTime < decisionCooldown) {
            return;
        }
        
        lastDecisionTime = currentTime;
        
        // 获取当前状态
        AIState currentState = stateManager.getCurrentState();
        
        // 根据状态和环境进行决策
        AIBehaviorType newBehaviorType = decideBehavior(currentState);
        
        // 如果需要切换行为
        if (newBehaviorType != currentBehaviorType) {
            setBehaviorType(newBehaviorType);
        }
    }
    
    /**
     * 决策逻辑
     */
    private AIBehaviorType decideBehavior(AIState currentState) {
        // 如果正在战斗，优先处理战斗
        if (currentState.isInCombat() && currentBehaviorType != AIBehaviorType.COMBAT) {
            return AIBehaviorType.COMBAT;
        }
        
        // 如果生命值低，寻找安全地点
        if (currentState.getHealthPercentage() < 0.3 && currentBehaviorType.isCombatBehavior()) {
            return AIBehaviorType.IDLE;
        }
        
        // 如果背包满了且在收集资源，切换到运输或建造
        if (currentState.isInventoryFull() && currentBehaviorType.isResourceGatheringBehavior()) {
            return AIBehaviorType.TRANSPORTING;
        }
        
        // 如果没有特殊情况，保持当前行为
        return currentBehaviorType;
    }
    
    /**
     * 设置行为类型
     */
    public void setBehaviorType(AIBehaviorType behaviorType) {
        if (behaviorType == currentBehaviorType) {
            return;
        }
        
        // 停止当前行为
        if (currentBehavior != null) {
            currentBehavior.stop();
        }
        
        // 切换到新行为
        currentBehaviorType = behaviorType;
        currentBehavior = behaviors.get(behaviorType);
        
        if (currentBehavior != null) {
            currentBehavior.start();
            LOGGER.debug("AI玩家 {} 切换到行为: {}", 
                aiPlayer.getAIName(), behaviorType.getDisplayName());
        } else {
            LOGGER.warn("未找到行为实现: {}", behaviorType);
        }
    }
    
    /**
     * 在指定位置执行动作
     */
    public void performActionAt(BlockPos pos, String action) {
        if (currentBehavior != null) {
            currentBehavior.performActionAt(pos, action);
        }
    }
    
    /**
     * 设置目标位置
     */
    public void setTarget(BlockPos target) {
        this.currentTarget = target;
        if (currentBehavior != null) {
            currentBehavior.setTarget(target);
        }
    }
    
    /**
     * 获取当前目标位置
     */
    @Nullable
    public BlockPos getCurrentTarget() {
        return currentTarget;
    }
    
    /**
     * 处理被攻击事件
     */
    public void onAttacked(LivingEntity attacker) {
        stateManager.onAttacked(attacker);
        
        // 如果不是战斗行为，切换到战斗
        if (!currentBehaviorType.isCombatBehavior()) {
            setBehaviorType(AIBehaviorType.COMBAT);
        }
    }
    
    /**
     * 获取当前行为类型
     */
    public AIBehaviorType getCurrentBehaviorType() {
        return currentBehaviorType;
    }
    
    /**
     * 获取当前行为实例
     */
    public AIBehavior getCurrentBehavior() {
        return currentBehavior;
    }
    
    /**
     * 获取状态管理器
     */
    public AIStateManager getStateManager() {
        return stateManager;
    }
    
    /**
     * 检查是否无敌
     */
    public boolean isInvulnerable() {
        return isInvulnerable || 
            MinecraftAIPlayerMod.getInstance().getConfig().aiPlayerSettings.aiPlayerInvulnerable;
    }
    
    /**
     * 设置无敌状态
     */
    public void setInvulnerable(boolean invulnerable) {
        this.isInvulnerable = invulnerable;
    }
    
    /**
     * 保存到NBT
     */
    public void writeToNbt(NbtCompound nbt) {
        nbt.putString("CurrentBehaviorType", currentBehaviorType.name());
        nbt.putBoolean("IsInvulnerable", isInvulnerable);
        nbt.putLong("LastDecisionTime", lastDecisionTime);
        
        if (currentTarget != null) {
            nbt.putLong("CurrentTarget", currentTarget.asLong());
        }
        
        // 保存状态管理器数据
        NbtCompound stateNbt = new NbtCompound();
        stateManager.writeToNbt(stateNbt);
        nbt.put("StateManager", stateNbt);
        
        // 保存当前行为数据
        if (currentBehavior != null) {
            NbtCompound behaviorNbt = new NbtCompound();
            currentBehavior.writeToNbt(behaviorNbt);
            nbt.put("CurrentBehavior", behaviorNbt);
        }
    }
    
    /**
     * 从NBT读取
     */
    public void readFromNbt(NbtCompound nbt) {
        if (nbt.contains("CurrentBehaviorType")) {
            AIBehaviorType behaviorType = AIBehaviorType.valueOf(nbt.getString("CurrentBehaviorType"));
            setBehaviorType(behaviorType);
        }
        
        isInvulnerable = nbt.getBoolean("IsInvulnerable");
        lastDecisionTime = nbt.getLong("LastDecisionTime");
        
        if (nbt.contains("CurrentTarget")) {
            currentTarget = BlockPos.fromLong(nbt.getLong("CurrentTarget"));
        }
        
        // 读取状态管理器数据
        if (nbt.contains("StateManager")) {
            stateManager.readFromNbt(nbt.getCompound("StateManager"));
        }
        
        // 读取当前行为数据
        if (nbt.contains("CurrentBehavior") && currentBehavior != null) {
            currentBehavior.readFromNbt(nbt.getCompound("CurrentBehavior"));
        }
    }
}
