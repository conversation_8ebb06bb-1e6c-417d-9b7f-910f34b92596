package com.aiplayer.ai.behavior.impl;

import com.aiplayer.ai.behavior.AIBehavior;
import com.aiplayer.ai.behavior.AIBehaviorType;
import com.aiplayer.entity.AIPlayerEntity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.mob.HostileEntity;
import net.minecraft.util.math.BlockPos;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * 战斗行为实现
 * AI玩家主动寻找和攻击敌对生物
 */
public class CombatBehavior extends AIBehavior {
    
    private LivingEntity currentTarget;
    private long lastAttackTime;
    
    public CombatBehavior(AIPlayerEntity aiPlayer) {
        super(aiPlayer);
        this.lastAttackTime = 0;
    }
    
    @Override
    public AIBehaviorType getBehaviorType() {
        return AIBehaviorType.COMBAT;
    }
    
    @Override
    protected void onStart() {
        currentTarget = null;
        lastAttackTime = 0;
    }
    
    @Override
    protected void onStop() {
        stopMoving();
        currentTarget = null;
    }
    
    @Override
    protected void onTick() {
        // 寻找敌对目标
        if (currentTarget == null || !currentTarget.isAlive()) {
            findTarget();
        }
        
        // 攻击目标
        if (currentTarget != null) {
            attackTarget();
        }
    }
    
    @Override
    protected void onTargetChanged(@Nullable BlockPos newTarget) {
        // 战斗行为不使用位置目标
    }
    
    @Override
    protected void onActionRequested(BlockPos pos, String action) {
        // TODO: 处理战斗相关动作
    }
    
    private void findTarget() {
        List<LivingEntity> nearbyEntities = aiPlayer.getWorld().getEntitiesByClass(
            LivingEntity.class, 
            aiPlayer.getBoundingBox().expand(16.0), 
            entity -> entity instanceof HostileEntity && entity.isAlive()
        );
        
        if (!nearbyEntities.isEmpty()) {
            currentTarget = nearbyEntities.get(0);
        }
    }
    
    private void attackTarget() {
        if (currentTarget == null) return;
        
        double distance = aiPlayer.distanceTo(currentTarget);
        
        if (distance > 16.0) {
            // 目标太远，放弃
            currentTarget = null;
            return;
        }
        
        if (distance > 2.0) {
            // 移动到目标
            aiPlayer.getNavigation().startMovingTo(currentTarget, 1.2);
        } else {
            // 攻击目标
            long currentTime = aiPlayer.getWorld().getTime();
            if (currentTime - lastAttackTime > 20) { // 1秒攻击间隔
                aiPlayer.tryAttack(currentTarget);
                lastAttackTime = currentTime;
            }
        }
    }
}
