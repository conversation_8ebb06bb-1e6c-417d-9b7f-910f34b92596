package com.aiplayer.ai;

import com.aiplayer.MinecraftAIPlayerMod;
import com.aiplayer.ai.behavior.AIBehaviorType;
import com.aiplayer.entity.AIPlayerEntity;
import com.aiplayer.entity.ModEntities;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.text.Text;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AI玩家管理器
 * 负责管理所有AI玩家的生命周期、行为和状态
 */
public class AIPlayerManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(AIPlayerManager.class);
    
    // AI玩家存储
    private final Map<Integer, AIPlayerEntity> aiPlayers = new ConcurrentHashMap<>();
    private final Map<UUID, Set<Integer>> playerOwnedAIs = new ConcurrentHashMap<>();
    
    // 管理状态
    private MinecraftServer server;
    private int nextAIId = 1;
    private long lastCleanupTime = 0;
    
    /**
     * 服务器启动时调用
     */
    public void onServerStart(MinecraftServer server) {
        this.server = server;
        LOGGER.info("AI玩家管理器启动");
    }
    
    /**
     * 服务器停止时调用
     */
    public void onServerStop(MinecraftServer server) {
        // 保存所有AI玩家数据
        saveAllAIPlayers();
        
        // 清理资源
        aiPlayers.clear();
        playerOwnedAIs.clear();
        
        LOGGER.info("AI玩家管理器停止");
    }
    
    /**
     * 玩家加入时调用
     */
    public void onPlayerJoin(ServerPlayerEntity player) {
        // 向玩家发送当前AI玩家信息
        sendAIPlayerListToPlayer(player);
    }
    
    /**
     * 服务器tick时调用
     */
    public void tick(MinecraftServer server) {
        // 定期清理无效的AI玩家
        long currentTime = server.getOverworld().getTime();
        if (currentTime - lastCleanupTime > 1200) { // 每分钟清理一次
            cleanupInvalidAIPlayers();
            lastCleanupTime = currentTime;
        }
    }
    
    /**
     * 生成AI玩家
     */
    public AIPlayerEntity spawnAIPlayer(ServerWorld world, double x, double y, double z, 
                                       String name, String behaviorType) {
        try {
            // 检查AI玩家数量限制
            if (aiPlayers.size() >= MinecraftAIPlayerMod.getInstance().getConfig()
                    .aiPlayerSettings.maxAIPlayers) {
                LOGGER.warn("已达到AI玩家数量上限: {}", aiPlayers.size());
                return null;
            }
            
            // 创建AI玩家实体
            AIPlayerEntity aiPlayer = new AIPlayerEntity(ModEntities.AI_PLAYER, world);
            
            // 设置位置和属性
            aiPlayer.setPosition(x, y, z);
            aiPlayer.setAIName(name != null ? name : generateDefaultName());
            aiPlayer.setBehaviorType(AIBehaviorType.valueOf(behaviorType.toUpperCase()));
            
            // 生成到世界中
            world.spawnEntity(aiPlayer);
            
            // 注册到管理器
            int aiId = nextAIId++;
            aiPlayers.put(aiId, aiPlayer);
            
            LOGGER.info("生成AI玩家: {} (ID: {}) 在位置 ({}, {}, {})", 
                name, aiId, x, y, z);
            
            return aiPlayer;
            
        } catch (Exception e) {
            LOGGER.error("生成AI玩家时出错", e);
            return null;
        }
    }
    
    /**
     * 生成AI玩家（由玩家创建）
     */
    public AIPlayerEntity spawnAIPlayerForPlayer(ServerPlayerEntity owner, Vec3d position, 
                                                String name, AIBehaviorType behaviorType) {
        AIPlayerEntity aiPlayer = spawnAIPlayer(owner.getServerWorld(), 
            position.x, position.y, position.z, name, behaviorType.name());
        
        if (aiPlayer != null) {
            // 设置所有者
            aiPlayer.setOwnerId(owner.getUuid());
            
            // 记录所有权关系
            playerOwnedAIs.computeIfAbsent(owner.getUuid(), k -> new HashSet<>())
                .add(getAIPlayerId(aiPlayer));
            
            // 通知玩家
            owner.sendMessage(Text.literal("成功创建AI玩家: " + name), false);
        }
        
        return aiPlayer;
    }
    
    /**
     * 移除AI玩家
     */
    public boolean removeAIPlayer(int aiId) {
        AIPlayerEntity aiPlayer = aiPlayers.remove(aiId);
        if (aiPlayer != null) {
            // 从世界中移除
            aiPlayer.discard();
            
            // 清理所有权关系
            UUID ownerId = aiPlayer.getOwnerId();
            if (ownerId != null) {
                Set<Integer> ownedAIs = playerOwnedAIs.get(ownerId);
                if (ownedAIs != null) {
                    ownedAIs.remove(aiId);
                    if (ownedAIs.isEmpty()) {
                        playerOwnedAIs.remove(ownerId);
                    }
                }
            }
            
            LOGGER.info("移除AI玩家: {} (ID: {})", aiPlayer.getAIName(), aiId);
            return true;
        }
        return false;
    }
    
    /**
     * 执行AI玩家命令
     */
    public boolean executeCommand(int aiId, String command, String[] args) {
        AIPlayerEntity aiPlayer = aiPlayers.get(aiId);
        if (aiPlayer == null) {
            LOGGER.warn("未找到AI玩家: {}", aiId);
            return false;
        }
        
        try {
            switch (command.toLowerCase()) {
                case "goto":
                    if (args.length >= 3) {
                        BlockPos target = new BlockPos(
                            Integer.parseInt(args[0]),
                            Integer.parseInt(args[1]),
                            Integer.parseInt(args[2])
                        );
                        aiPlayer.setTarget(target);
                        return true;
                    }
                    break;
                    
                case "behavior":
                    if (args.length >= 1) {
                        AIBehaviorType behaviorType = AIBehaviorType.valueOf(args[0].toUpperCase());
                        aiPlayer.setBehaviorType(behaviorType);
                        return true;
                    }
                    break;
                    
                case "action":
                    if (args.length >= 4) {
                        BlockPos pos = new BlockPos(
                            Integer.parseInt(args[0]),
                            Integer.parseInt(args[1]),
                            Integer.parseInt(args[2])
                        );
                        aiPlayer.performActionAt(pos, args[3]);
                        return true;
                    }
                    break;
                    
                case "pause":
                    aiPlayer.setActive(false);
                    return true;
                    
                case "resume":
                    aiPlayer.setActive(true);
                    return true;
                    
                default:
                    LOGGER.warn("未知命令: {}", command);
                    return false;
            }
        } catch (Exception e) {
            LOGGER.error("执行AI玩家命令时出错: {}", command, e);
        }
        
        return false;
    }
    
    /**
     * 获取AI玩家
     */
    public AIPlayerEntity getAIPlayer(int aiId) {
        return aiPlayers.get(aiId);
    }
    
    /**
     * 获取所有AI玩家
     */
    public Collection<AIPlayerEntity> getAllAIPlayers() {
        return aiPlayers.values();
    }
    
    /**
     * 获取玩家拥有的AI玩家
     */
    public Set<AIPlayerEntity> getPlayerOwnedAIPlayers(UUID playerId) {
        Set<Integer> aiIds = playerOwnedAIs.get(playerId);
        if (aiIds == null) {
            return Collections.emptySet();
        }
        
        Set<AIPlayerEntity> result = new HashSet<>();
        for (int aiId : aiIds) {
            AIPlayerEntity aiPlayer = aiPlayers.get(aiId);
            if (aiPlayer != null) {
                result.add(aiPlayer);
            }
        }
        return result;
    }
    
    /**
     * 生成默认名称
     */
    private String generateDefaultName() {
        String prefix = MinecraftAIPlayerMod.getInstance().getConfig()
            .aiPlayerSettings.defaultAIName;
        return prefix + "_" + nextAIId;
    }
    
    /**
     * 获取AI玩家ID
     */
    private int getAIPlayerId(AIPlayerEntity aiPlayer) {
        for (Map.Entry<Integer, AIPlayerEntity> entry : aiPlayers.entrySet()) {
            if (entry.getValue() == aiPlayer) {
                return entry.getKey();
            }
        }
        return -1;
    }
    
    /**
     * 清理无效的AI玩家
     */
    private void cleanupInvalidAIPlayers() {
        Iterator<Map.Entry<Integer, AIPlayerEntity>> iterator = aiPlayers.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer, AIPlayerEntity> entry = iterator.next();
            AIPlayerEntity aiPlayer = entry.getValue();
            
            if (aiPlayer.isRemoved() || !aiPlayer.isAlive()) {
                iterator.remove();
                LOGGER.debug("清理无效AI玩家: {}", entry.getKey());
            }
        }
    }
    
    /**
     * 保存所有AI玩家数据
     */
    private void saveAllAIPlayers() {
        // 这里可以实现AI玩家数据的持久化
        LOGGER.info("保存 {} 个AI玩家的数据", aiPlayers.size());
    }
    
    /**
     * 向玩家发送AI玩家列表
     */
    private void sendAIPlayerListToPlayer(ServerPlayerEntity player) {
        // 这里可以实现向客户端发送AI玩家信息的逻辑
        LOGGER.debug("向玩家 {} 发送AI玩家列表", player.getName().getString());
    }
}
