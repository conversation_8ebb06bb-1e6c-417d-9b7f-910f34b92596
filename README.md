# Minecraft AI Player Mod

一个为《我的世界》(Minecraft) 1.20.1 开发的智能AI玩家模组，使用Fabric框架构建。

## 功能特性

### 🤖 智能AI玩家
- **多种行为模式**: 挖矿、建造、农业、战斗、探索、收集、守卫、跟随、交易、维修、运输
- **智能决策系统**: 基于状态机的AI决策，能够根据环境和状态自动切换行为
- **自主学习**: AI玩家能够适应不同的环境和任务需求

### 🎮 玩家交互
- **命令控制**: 完整的命令系统，支持生成、控制、配置AI玩家
- **GUI界面**: 直观的图形界面，按G键打开控制面板
- **实时监控**: 查看AI玩家状态、行为和性能信息

### ⚙️ 高度可配置
- **行为参数**: 可调整各种行为的范围、优先级和执行策略
- **性能优化**: 可配置的更新间隔和并发限制
- **调试支持**: 详细的日志记录和调试信息

## 安装要求

- **Minecraft版本**: 1.20.1
- **模组加载器**: Fabric Loader 0.16.14+
- **前置模组**: Fabric API 0.92.6+
- **Java版本**: Java 17+

## 安装方法

1. 确保已安装Fabric Loader和Fabric API
2. 下载模组文件 `minecraft-ai-player-1.0.0.jar`
3. 将模组文件放入 `.minecraft/mods` 文件夹
4. 启动游戏

## 使用指南

### 基本命令

```
/aiplayer spawn <名称> <行为类型> [位置]    # 生成AI玩家
/aiplayer remove <目标>                    # 移除AI玩家
/aiplayer behavior <目标> <行为类型>       # 设置行为
/aiplayer goto <目标> <位置>               # 移动到位置
/aiplayer pause <目标>                     # 暂停AI玩家
/aiplayer resume <目标>                    # 恢复AI玩家
/aiplayer list                             # 列出所有AI玩家
/aiplayer info <目标>                      # 查看AI玩家信息
```

### 行为类型

- `idle` - 空闲状态，随机漫步
- `mining` - 自动挖矿，寻找和挖掘矿物
- `building` - 建造结构，根据指令建造
- `farming` - 农业活动，种植和收获作物
- `combat` - 战斗模式，攻击敌对生物
- `exploration` - 探索模式，探索未知区域
- `collecting` - 收集模式，收集掉落物品
- `guarding` - 守卫模式，保护指定区域
- `following` - 跟随模式，跟随指定玩家
- `trading` - 交易模式，与村民交易
- `repairing` - 维修模式，修复损坏结构
- `transporting` - 运输模式，在地点间运输物品

### GUI控制面板

按 `G` 键打开AI玩家控制面板，可以：
- 生成新的AI玩家
- 选择行为类型
- 设置AI玩家名称
- 快速访问常用功能

### 配置文件

配置文件位于 `.minecraft/config/minecraft-ai-player.json`

主要配置选项：
```json
{
  "aiPlayerSettings": {
    "maxAIPlayers": 10,
    "enableAI": true,
    "defaultAIName": "AI_Player",
    "showAINameTags": true,
    "aiPlayerHealth": 20.0,
    "aiPlayerInvulnerable": false
  },
  "behaviorSettings": {
    "enableMining": true,
    "enableBuilding": true,
    "enableFarming": true,
    "enableCombat": true,
    "miningRange": 32,
    "buildingRange": 16,
    "farmingRange": 24,
    "combatRange": 16
  },
  "performanceSettings": {
    "aiTickInterval": 20,
    "pathfindingUpdateInterval": 10,
    "maxPathfindingDistance": 128,
    "enableDebugLogging": false,
    "maxConcurrentAIOperations": 5
  }
}
```

## 开发信息

### 项目结构

```
src/main/java/com/aiplayer/
├── MinecraftAIPlayerMod.java          # 主模组类
├── ai/                                # AI系统
│   ├── AIPlayerManager.java           # AI玩家管理器
│   ├── AIBehaviorManager.java         # 行为管理器
│   ├── behavior/                      # 行为实现
│   ├── state/                         # 状态管理
│   └── goal/                          # 目标管理
├── entity/                            # 实体类
│   ├── AIPlayerEntity.java            # AI玩家实体
│   └── ModEntities.java               # 实体注册
├── command/                           # 命令系统
├── config/                            # 配置管理
├── network/                           # 网络通信
├── client/                            # 客户端代码
├── util/                              # 工具类
├── exception/                         # 异常处理
└── mixin/                             # Mixin扩展
```

### 扩展开发

要添加新的AI行为：

1. 创建新的行为类继承 `AIBehavior`
2. 在 `AIBehaviorType` 枚举中添加新类型
3. 在 `AIBehaviorManager` 中注册新行为
4. 实现具体的行为逻辑

示例：
```java
public class CustomBehavior extends AIBehavior {
    public CustomBehavior(AIPlayerEntity aiPlayer) {
        super(aiPlayer);
    }
    
    @Override
    public AIBehaviorType getBehaviorType() {
        return AIBehaviorType.CUSTOM;
    }
    
    @Override
    protected void onStart() {
        // 行为开始时的逻辑
    }
    
    @Override
    protected void onTick() {
        // 每tick执行的逻辑
    }
    
    // 实现其他抽象方法...
}
```

## 故障排除

### 常见问题

1. **AI玩家不响应命令**
   - 检查AI玩家是否处于活跃状态
   - 确认命令语法正确
   - 查看服务器日志中的错误信息

2. **性能问题**
   - 减少同时活跃的AI玩家数量
   - 调整配置文件中的更新间隔
   - 启用性能监控日志

3. **AI玩家行为异常**
   - 检查目标区域是否可达
   - 确认AI玩家有必要的工具和材料
   - 重置AI玩家状态

### 调试模式

在配置文件中启用 `enableDebugLogging` 可以获得详细的调试信息。

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交问题报告和功能请求！如果您想贡献代码，请：

1. Fork 本仓库
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 更新日志

### v1.0.0
- 初始版本发布
- 实现基本的AI玩家功能
- 支持12种行为模式
- 完整的命令系统和GUI界面
- 可配置的性能和行为参数

## 联系方式

- 项目主页: https://github.com/minecraft-ai-player/mod
- 问题报告: https://github.com/minecraft-ai-player/mod/issues
- 讨论区: https://github.com/minecraft-ai-player/mod/discussions
