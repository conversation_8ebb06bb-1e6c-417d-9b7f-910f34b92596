package com.aiplayer.entity;

import com.aiplayer.MinecraftAIPlayerMod;
import net.fabricmc.fabric.api.object.builder.v1.entity.FabricDefaultAttributeRegistry;
import net.fabricmc.fabric.api.object.builder.v1.entity.FabricEntityTypeBuilder;
import net.minecraft.entity.EntityDimensions;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.SpawnGroup;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;

/**
 * 模组实体注册类
 * 负责注册AI玩家实体和相关属性
 */
public class ModEntities {
    
    /**
     * AI玩家实体类型
     */
    public static final EntityType<AIPlayerEntity> AI_PLAYER = Registry.register(
        Registries.ENTITY_TYPE,
        MinecraftAIPlayerMod.id("ai_player"),
        FabricEntityTypeBuilder.create(SpawnGroup.MISC, AIPlayerEntity::new)
            .dimensions(EntityDimensions.fixed(0.6f, 1.8f)) // 玩家尺寸
            .trackRangeBlocks(64) // 追踪范围
            .trackedUpdateRate(3) // 更新频率
            .build()
    );
    
    /**
     * 注册所有实体类型和属性
     */
    public static void register() {
        MinecraftAIPlayerMod.LOGGER.info("注册模组实体类型...");
        
        // 注册AI玩家实体属性
        FabricDefaultAttributeRegistry.register(AI_PLAYER, AIPlayerEntity.createAIPlayerAttributes());
        
        MinecraftAIPlayerMod.LOGGER.info("模组实体类型注册完成");
    }
}
