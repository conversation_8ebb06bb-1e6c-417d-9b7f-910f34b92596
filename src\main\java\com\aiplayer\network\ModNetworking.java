package com.aiplayer.network;

import com.aiplayer.MinecraftAIPlayerMod;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

/**
 * 模组网络通信类
 * 处理客户端和服务器之间的数据包通信
 */
public class ModNetworking {
    
    // 网络数据包标识符
    public static final Identifier AI_PLAYER_SPAWN_PACKET = MinecraftAIPlayerMod.id("ai_player_spawn");
    public static final Identifier AI_PLAYER_COMMAND_PACKET = MinecraftAIPlayerMod.id("ai_player_command");
    public static final Identifier AI_PLAYER_STATUS_PACKET = MinecraftAIPlayerMod.id("ai_player_status");
    public static final Identifier AI_PLAYER_CONFIG_PACKET = MinecraftAIPlayerMod.id("ai_player_config");
    
    /**
     * 初始化网络通信
     */
    public static void init() {
        MinecraftAIPlayerMod.LOGGER.info("初始化网络通信...");
        
        // 注册服务器端数据包处理器
        registerServerPacketHandlers();
        
        MinecraftAIPlayerMod.LOGGER.info("网络通信初始化完成");
    }
    
    /**
     * 注册服务器端数据包处理器
     */
    private static void registerServerPacketHandlers() {
        // AI玩家生成数据包
        ServerPlayNetworking.registerGlobalReceiver(AI_PLAYER_SPAWN_PACKET, 
            (server, player, handler, buf, responseSender) -> {
                handleAIPlayerSpawnPacket(player, buf);
            });
        
        // AI玩家命令数据包
        ServerPlayNetworking.registerGlobalReceiver(AI_PLAYER_COMMAND_PACKET, 
            (server, player, handler, buf, responseSender) -> {
                handleAIPlayerCommandPacket(player, buf);
            });
        
        // AI玩家配置数据包
        ServerPlayNetworking.registerGlobalReceiver(AI_PLAYER_CONFIG_PACKET, 
            (server, player, handler, buf, responseSender) -> {
                handleAIPlayerConfigPacket(player, buf);
            });
    }
    
    /**
     * 处理AI玩家生成数据包
     */
    private static void handleAIPlayerSpawnPacket(ServerPlayerEntity player, PacketByteBuf buf) {
        try {
            double x = buf.readDouble();
            double y = buf.readDouble();
            double z = buf.readDouble();
            String aiName = buf.readString();
            String behaviorType = buf.readString();
            
            // 在服务器线程中执行
            player.getServer().execute(() -> {
                MinecraftAIPlayerMod.getInstance().getAIPlayerManager()
                    .spawnAIPlayer(player.getServerWorld(), x, y, z, aiName, behaviorType);
            });
            
        } catch (Exception e) {
            MinecraftAIPlayerMod.LOGGER.error("处理AI玩家生成数据包时出错", e);
        }
    }
    
    /**
     * 处理AI玩家命令数据包
     */
    private static void handleAIPlayerCommandPacket(ServerPlayerEntity player, PacketByteBuf buf) {
        try {
            int aiPlayerId = buf.readInt();
            String command = buf.readString();
            String[] args = new String[buf.readInt()];
            for (int i = 0; i < args.length; i++) {
                args[i] = buf.readString();
            }
            
            // 在服务器线程中执行
            player.getServer().execute(() -> {
                MinecraftAIPlayerMod.getInstance().getAIPlayerManager()
                    .executeCommand(aiPlayerId, command, args);
            });
            
        } catch (Exception e) {
            MinecraftAIPlayerMod.LOGGER.error("处理AI玩家命令数据包时出错", e);
        }
    }
    
    /**
     * 处理AI玩家配置数据包
     */
    private static void handleAIPlayerConfigPacket(ServerPlayerEntity player, PacketByteBuf buf) {
        try {
            String configKey = buf.readString();
            String configValue = buf.readString();
            
            // 在服务器线程中执行
            player.getServer().execute(() -> {
                // 这里可以添加配置更新逻辑
                MinecraftAIPlayerMod.LOGGER.info("收到配置更新: {} = {}", configKey, configValue);
            });
            
        } catch (Exception e) {
            MinecraftAIPlayerMod.LOGGER.error("处理AI玩家配置数据包时出错", e);
        }
    }
}
