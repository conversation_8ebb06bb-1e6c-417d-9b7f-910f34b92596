package com.aiplayer.ai.state;

import com.aiplayer.entity.AIPlayerEntity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.inventory.Inventory;

/**
 * AI状态类
 * 记录AI玩家的当前状态信息
 */
public class AIState {
    
    private final AIPlayerEntity aiPlayer;
    
    // 状态信息
    private boolean inCombat;
    private LivingEntity lastAttacker;
    private long lastAttackTime;
    private boolean inventoryFull;
    private double healthPercentage;
    private double hungerPercentage;
    
    public AIState(AIPlayerEntity aiPlayer) {
        this.aiPlayer = aiPlayer;
        this.inCombat = false;
        this.lastAttacker = null;
        this.lastAttackTime = 0;
        this.inventoryFull = false;
        this.healthPercentage = 1.0;
        this.hungerPercentage = 1.0;
    }
    
    /**
     * 更新状态信息
     */
    public void update() {
        updateHealthStatus();
        updateInventoryStatus();
        updateCombatStatus();
    }
    
    /**
     * 更新生命值状态
     */
    private void updateHealthStatus() {
        float maxHealth = aiPlayer.getMaxHealth();
        float currentHealth = aiPlayer.getHealth();
        this.healthPercentage = currentHealth / maxHealth;
    }
    
    /**
     * 更新背包状态
     */
    private void updateInventoryStatus() {
        Inventory inventory = aiPlayer.getInventory();
        int emptySlots = 0;
        
        for (int i = 0; i < inventory.size(); i++) {
            if (inventory.getStack(i).isEmpty()) {
                emptySlots++;
            }
        }
        
        this.inventoryFull = emptySlots <= 2; // 少于2个空槽位认为背包满了
    }
    
    /**
     * 更新战斗状态
     */
    private void updateCombatStatus() {
        long currentTime = aiPlayer.getWorld().getTime();
        
        // 如果5秒内没有被攻击，退出战斗状态
        if (inCombat && currentTime - lastAttackTime > 100) {
            inCombat = false;
            lastAttacker = null;
        }
    }
    
    /**
     * 被攻击时调用
     */
    public void onAttacked(LivingEntity attacker) {
        this.inCombat = true;
        this.lastAttacker = attacker;
        this.lastAttackTime = aiPlayer.getWorld().getTime();
    }
    
    // Getter方法
    
    public boolean isInCombat() {
        return inCombat;
    }
    
    public LivingEntity getLastAttacker() {
        return lastAttacker;
    }
    
    public long getLastAttackTime() {
        return lastAttackTime;
    }
    
    public boolean isInventoryFull() {
        return inventoryFull;
    }
    
    public double getHealthPercentage() {
        return healthPercentage;
    }
    
    public double getHungerPercentage() {
        return hungerPercentage;
    }
    
    public boolean isLowHealth() {
        return healthPercentage < 0.3;
    }
    
    public boolean isCriticalHealth() {
        return healthPercentage < 0.1;
    }
    
    public boolean isHealthy() {
        return healthPercentage > 0.8;
    }
}
