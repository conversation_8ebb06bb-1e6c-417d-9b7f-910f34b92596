package com.aiplayer.ai.state;

import com.aiplayer.entity.AIPlayerEntity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.nbt.NbtCompound;

/**
 * AI状态管理器
 * 管理AI玩家的状态信息和状态变化
 */
public class AIStateManager {
    
    private final AIPlayerEntity aiPlayer;
    private final AIState currentState;
    
    public AIStateManager(AIPlayerEntity aiPlayer) {
        this.aiPlayer = aiPlayer;
        this.currentState = new AIState(aiPlayer);
    }
    
    /**
     * 每tick更新状态
     */
    public void tick() {
        currentState.update();
    }
    
    /**
     * 处理被攻击事件
     */
    public void onAttacked(LivingEntity attacker) {
        currentState.onAttacked(attacker);
    }
    
    /**
     * 获取当前状态
     */
    public AIState getCurrentState() {
        return currentState;
    }
    
    /**
     * 保存状态到NBT
     */
    public void writeToNbt(NbtCompound nbt) {
        nbt.putBoolean("InCombat", currentState.isInCombat());
        nbt.putLong("LastAttackTime", currentState.getLastAttackTime());
        nbt.putBoolean("InventoryFull", currentState.isInventoryFull());
        nbt.putDouble("HealthPercentage", currentState.getHealthPercentage());
        nbt.putDouble("HungerPercentage", currentState.getHungerPercentage());
    }
    
    /**
     * 从NBT读取状态
     */
    public void readFromNbt(NbtCompound nbt) {
        // 状态信息会在下次tick时自动更新，这里只需要读取关键信息
        if (nbt.getBoolean("InCombat")) {
            // 如果之前在战斗中，设置攻击时间
            long lastAttackTime = nbt.getLong("LastAttackTime");
            // 这里可以根据需要恢复战斗状态
        }
    }
}
