package com.aiplayer.exception;

/**
 * AI配置异常类
 * 当配置文件读取或写入出错时抛出
 */
public class AIConfigException extends AIPlayerException {
    
    private final String configKey;
    
    public AIConfigException(String configKey, String message) {
        super("配置处理", message);
        this.configKey = configKey;
    }
    
    public AIConfigException(String configKey, String message, Throwable cause) {
        super("配置处理", message, cause);
        this.configKey = configKey;
    }
    
    public String getConfigKey() {
        return configKey;
    }
}
