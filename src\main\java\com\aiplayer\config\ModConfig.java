package com.aiplayer.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import net.fabricmc.loader.api.FabricLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * 模组配置类
 * 管理AI玩家的各种配置选项
 */
public class ModConfig {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(ModConfig.class);
    private static final String CONFIG_FILE_NAME = "minecraft-ai-player.json";
    
    private final Gson gson = new GsonBuilder().setPrettyPrinting().create();
    private final Path configPath;
    
    // 配置选项
    public AIPlayerSettings aiPlayerSettings = new AIPlayerSettings();
    public BehaviorSettings behaviorSettings = new BehaviorSettings();
    public PerformanceSettings performanceSettings = new PerformanceSettings();
    
    public ModConfig() {
        this.configPath = FabricLoader.getInstance().getConfigDir().resolve(CONFIG_FILE_NAME);
    }
    
    /**
     * 加载配置文件
     */
    public void load() {
        try {
            if (Files.exists(configPath)) {
                String json = Files.readString(configPath);
                ModConfig loadedConfig = gson.fromJson(json, ModConfig.class);
                
                if (loadedConfig != null) {
                    this.aiPlayerSettings = loadedConfig.aiPlayerSettings != null ? 
                        loadedConfig.aiPlayerSettings : new AIPlayerSettings();
                    this.behaviorSettings = loadedConfig.behaviorSettings != null ? 
                        loadedConfig.behaviorSettings : new BehaviorSettings();
                    this.performanceSettings = loadedConfig.performanceSettings != null ? 
                        loadedConfig.performanceSettings : new PerformanceSettings();
                }
                
                LOGGER.info("配置文件加载成功: {}", configPath);
            } else {
                // 创建默认配置文件
                save();
                LOGGER.info("创建默认配置文件: {}", configPath);
            }
        } catch (IOException e) {
            LOGGER.error("加载配置文件失败: {}", configPath, e);
        }
    }
    
    /**
     * 保存配置文件
     */
    public void save() {
        try {
            Files.createDirectories(configPath.getParent());
            String json = gson.toJson(this);
            Files.writeString(configPath, json);
            LOGGER.info("配置文件保存成功: {}", configPath);
        } catch (IOException e) {
            LOGGER.error("保存配置文件失败: {}", configPath, e);
        }
    }
    
    /**
     * AI玩家基本设置
     */
    public static class AIPlayerSettings {
        public int maxAIPlayers = 10; // 最大AI玩家数量
        public boolean enableAI = true; // 是否启用AI功能
        public String defaultAIName = "AI_Player"; // 默认AI玩家名称前缀
        public boolean showAINameTags = true; // 是否显示AI玩家名牌
        public double aiPlayerHealth = 20.0; // AI玩家生命值
        public boolean aiPlayerInvulnerable = false; // AI玩家是否无敌
    }
    
    /**
     * 行为设置
     */
    public static class BehaviorSettings {
        public boolean enableMining = true; // 启用挖矿行为
        public boolean enableBuilding = true; // 启用建造行为
        public boolean enableFarming = true; // 启用农业行为
        public boolean enableCombat = true; // 启用战斗行为
        public boolean enableExploration = true; // 启用探索行为
        
        public int miningRange = 32; // 挖矿范围
        public int buildingRange = 16; // 建造范围
        public int farmingRange = 24; // 农业范围
        public int combatRange = 16; // 战斗范围
        public int explorationRange = 64; // 探索范围
        
        public double taskSwitchCooldown = 5.0; // 任务切换冷却时间（秒）
        public double decisionInterval = 1.0; // 决策间隔时间（秒）
    }
    
    /**
     * 性能设置
     */
    public static class PerformanceSettings {
        public int aiTickInterval = 20; // AI更新间隔（tick）
        public int pathfindingUpdateInterval = 10; // 寻路更新间隔（tick）
        public int maxPathfindingDistance = 128; // 最大寻路距离
        public boolean enableDebugLogging = false; // 启用调试日志
        public int maxConcurrentAIOperations = 5; // 最大并发AI操作数
    }
}
