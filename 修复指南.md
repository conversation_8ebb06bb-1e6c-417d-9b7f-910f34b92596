# Minecraft AI Player 模组编译错误修复指南

## 问题分析

根据IDE显示的错误信息，主要存在以下问题：

### 1. 包声明不匹配错误
- 错误信息显示期望包名为 `src.main.java.com.aiplayer.ai.goal`
- 实际包名为 `com.aiplayer.ai.goal`
- 这表明IDE的项目配置可能有问题

### 2. 类型无法解析错误
- `AIPlayerEntity` 类型无法解析
- `LivingEntity` 类型无法解析  
- `SwimGoal` 等Minecraft类型无法解析

## 修复步骤

### 步骤1: 检查项目结构
确保项目结构正确：
```
src/
  main/
    java/
      com/
        aiplayer/
          MinecraftAIPlayerMod.java
          ai/
            AIBehaviorManager.java
            AIPlayerManager.java
            behavior/
            goal/
            state/
          entity/
            AIPlayerEntity.java
            ModEntities.java
          ...
    resources/
      fabric.mod.json
      minecraft-ai-player.mixins.json
```

### 步骤2: 清理并重新构建
```bash
# 清理构建缓存
gradlew clean

# 重新生成映射
gradlew genSources

# 重新构建
gradlew build
```

### 步骤3: IDE配置修复
如果使用IntelliJ IDEA：
1. 删除 `.idea` 文件夹
2. 重新导入项目
3. 确保选择正确的Gradle项目
4. 等待索引完成

如果使用VSCode：
1. 重新加载窗口 (Ctrl+Shift+P -> "Developer: Reload Window")
2. 确保Java扩展包已安装
3. 检查Java路径配置

### 步骤4: 依赖检查
确保 `gradle.properties` 中的版本正确：
```properties
minecraft_version=1.20.1
yarn_mappings=1.20.1+build.10
loader_version=0.16.14
fabric_version=0.92.6*****.1
```

### 步骤5: 常见问题修复

#### 问题1: 包声明错误
如果IDE仍然显示包声明错误，检查：
- 文件是否在正确的目录结构中
- 包声明是否与目录结构匹配
- 是否有重复的类名

#### 问题2: Minecraft类型无法解析
这通常是映射问题：
```bash
# 重新生成映射
gradlew genSources
```

#### 问题3: 自定义类型无法解析
检查：
- 类文件是否存在
- 包声明是否正确
- 导入语句是否正确

## 验证修复

修复完成后，运行以下命令验证：

```bash
# 编译检查
gradlew compileJava

# 运行测试
gradlew test

# 完整构建
gradlew build
```

## 如果问题仍然存在

1. 检查Java版本 (需要Java 17+)
2. 检查Gradle版本兼容性
3. 清理所有缓存：
   ```bash
   gradlew clean
   rm -rf .gradle
   rm -rf build
   gradlew build
   ```

4. 重新创建项目：
   - 备份源代码
   - 使用Fabric模板重新创建项目
   - 复制源代码到新项目

## 联系支持

如果以上步骤都无法解决问题，请提供：
- 完整的错误日志
- Java版本信息
- Gradle版本信息
- 操作系统信息
