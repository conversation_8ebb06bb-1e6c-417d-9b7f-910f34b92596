# AI玩家模组使用指南

## 快速开始

### 第一步：生成AI玩家

最简单的方式是使用GUI界面：
1. 在游戏中按 `G` 键打开控制面板
2. 输入AI玩家名称
3. 选择行为类型
4. 点击"生成AI玩家"

或者使用命令：
```
/aiplayer spawn MyAI mining
```

### 第二步：观察AI玩家

生成后，AI玩家会出现在你附近并开始执行指定的行为。你可以：
- 观察AI玩家的行为模式
- 使用 `/aiplayer info <AI玩家>` 查看详细信息
- 使用 `/aiplayer list` 查看所有AI玩家

### 第三步：控制AI玩家

```bash
# 让AI玩家前往指定位置
/aiplayer goto MyAI 100 64 200

# 改变AI玩家的行为
/aiplayer behavior MyAI building

# 暂停AI玩家
/aiplayer pause MyAI

# 恢复AI玩家
/aiplayer resume MyAI
```

## 详细功能说明

### 行为模式详解

#### 挖矿模式 (mining)
- **功能**: 自动寻找和挖掘矿物
- **范围**: 默认16格半径
- **目标**: 煤矿、铁矿、金矿、钻石矿等
- **特点**: 会自动寻找相邻的同类矿物

使用示例：
```bash
/aiplayer spawn Miner mining
/aiplayer goto Miner 50 12 50  # 让矿工前往地下
```

#### 建造模式 (building)
- **功能**: 根据指令建造结构
- **材料**: 自动获得基础建造材料
- **结构**: 支持墙壁、房屋等基础结构

使用示例：
```bash
/aiplayer spawn Builder building
/aiplayer action Builder wall 100 64 100    # 建造墙壁
/aiplayer action Builder house 110 64 110   # 建造房屋
```

#### 农业模式 (farming)
- **功能**: 种植和收获农作物
- **作物**: 小麦、胡萝卜、土豆等
- **自动化**: 自动耕地、播种、收获

#### 战斗模式 (combat)
- **功能**: 主动寻找和攻击敌对生物
- **范围**: 默认16格半径
- **目标**: 僵尸、骷髅、爬行者等敌对生物
- **装备**: 自动获得基础武器

#### 探索模式 (exploration)
- **功能**: 探索未知区域
- **范围**: 默认64格半径
- **特点**: 会记录已探索的区域

#### 收集模式 (collecting)
- **功能**: 收集地面上的掉落物品
- **优先级**: 优先收集稀有物品
- **存储**: 自动整理背包

#### 守卫模式 (guarding)
- **功能**: 保护指定区域
- **范围**: 以生成点为中心的区域
- **行为**: 攻击进入区域的敌对生物

#### 跟随模式 (following)
- **功能**: 跟随指定玩家
- **距离**: 保持2-5格距离
- **行为**: 模仿玩家的基本行为

### 高级功能

#### 批量管理
```bash
# 生成多个AI玩家
/aiplayer spawn Miner1 mining 50 12 50
/aiplayer spawn Miner2 mining 60 12 60
/aiplayer spawn Builder1 building 100 64 100

# 批量设置行为
/aiplayer behavior @e[type=minecraft-ai-player:ai_player] idle
```

#### 区域控制
```bash
# 设置挖矿区域
/aiplayer action Miner mine 45 10 45
/aiplayer action Miner mine 55 15 55

# 设置建造区域
/aiplayer action Builder build 95 64 95
```

#### 状态监控
```bash
# 查看所有AI玩家状态
/aiplayer list

# 查看特定AI玩家详细信息
/aiplayer info Miner1

# 检查AI玩家健康状况
/aiplayer info @e[type=minecraft-ai-player:ai_player]
```

## 配置优化

### 性能优化

如果遇到性能问题，可以调整以下配置：

```json
{
  "performanceSettings": {
    "aiTickInterval": 40,              // 增加更新间隔
    "maxConcurrentAIOperations": 3,    // 减少并发操作
    "maxPathfindingDistance": 64       // 减少寻路距离
  }
}
```

### 行为调整

```json
{
  "behaviorSettings": {
    "miningRange": 16,        // 减少挖矿范围
    "combatRange": 8,         // 减少战斗范围
    "taskSwitchCooldown": 10.0 // 增加任务切换冷却
  }
}
```

## 故障排除

### AI玩家不工作

1. **检查状态**：
   ```bash
   /aiplayer info <AI玩家名称>
   ```

2. **确认是否暂停**：
   ```bash
   /aiplayer resume <AI玩家名称>
   ```

3. **重置行为**：
   ```bash
   /aiplayer behavior <AI玩家名称> idle
   /aiplayer behavior <AI玩家名称> <原行为>
   ```

### AI玩家卡住

1. **传送到新位置**：
   ```bash
   /aiplayer goto <AI玩家名称> <新坐标>
   ```

2. **重置目标**：
   ```bash
   /aiplayer action <AI玩家名称> stop 0 0 0
   ```

### 性能问题

1. **减少AI玩家数量**：
   ```bash
   /aiplayer remove @e[type=minecraft-ai-player:ai_player,limit=5]
   ```

2. **暂停部分AI玩家**：
   ```bash
   /aiplayer pause @e[type=minecraft-ai-player:ai_player,limit=3]
   ```

## 最佳实践

### 1. 合理分配任务
- 不要让所有AI玩家执行相同任务
- 根据需求分配不同的行为模式
- 定期检查AI玩家的工作效率

### 2. 区域规划
- 为不同行为的AI玩家分配不同区域
- 避免AI玩家之间的冲突
- 预留足够的工作空间

### 3. 资源管理
- 定期清理AI玩家的背包
- 为AI玩家提供必要的工具和材料
- 建立资源收集和分配系统

### 4. 监控和维护
- 定期检查AI玩家状态
- 及时处理异常情况
- 根据需要调整配置参数

## 进阶技巧

### 自动化农场
```bash
# 创建自动化小麦农场
/aiplayer spawn Farmer1 farming 200 64 200
/aiplayer spawn Farmer2 farming 210 64 200
/aiplayer spawn Collector collecting 205 64 205
```

### 自动化矿场
```bash
# 创建多层矿场
/aiplayer spawn DeepMiner mining 0 12 0
/aiplayer spawn MidMiner mining 0 32 0
/aiplayer spawn SurfaceMiner mining 0 52 0
```

### 防御系统
```bash
# 创建基地防御
/aiplayer spawn Guard1 guarding 100 64 100
/aiplayer spawn Guard2 guarding 120 64 120
/aiplayer spawn Patrol combat 110 64 110
```
