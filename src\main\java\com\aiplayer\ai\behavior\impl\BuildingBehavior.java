package com.aiplayer.ai.behavior.impl;

import com.aiplayer.ai.behavior.AIBehavior;
import com.aiplayer.ai.behavior.AIBehaviorType;
import com.aiplayer.entity.AIPlayerEntity;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.World;
import org.jetbrains.annotations.Nullable;

import java.util.*;

/**
 * 建造行为实现
 * AI玩家根据蓝图或模式建造结构
 */
public class BuildingBehavior extends AIBehavior {
    
    private Queue<BuildTask> buildQueue;
    private BuildTask currentTask;
    private boolean isPlacing;
    
    public BuildingBehavior(AIPlayerEntity aiPlayer) {
        super(aiPlayer);
        this.buildQueue = new LinkedList<>();
        this.isPlacing = false;
    }
    
    @Override
    public AIBehaviorType getBehaviorType() {
        return AIBehaviorType.BUILDING;
    }
    
    @Override
    protected void onStart() {
        buildQueue.clear();
        currentTask = null;
        isPlacing = false;
        ensureBuildingMaterials();
    }
    
    @Override
    protected void onStop() {
        stopMoving();
        isPlacing = false;
    }
    
    @Override
    protected void onTick() {
        if (currentTask != null) {
            processBuilding();
        } else if (!buildQueue.isEmpty()) {
            currentTask = buildQueue.poll();
            if (currentTask != null) {
                setTarget(currentTask.position);
            }
        }
    }
    
    @Override
    protected void onTargetChanged(@Nullable BlockPos newTarget) {
        if (newTarget != null) {
            // 创建简单的建造任务
            buildQueue.offer(new BuildTask(newTarget, Blocks.COBBLESTONE.getDefaultState()));
        }
    }
    
    @Override
    protected void onActionRequested(BlockPos pos, String action) {
        switch (action.toLowerCase()) {
            case "build":
                setTarget(pos);
                break;
            case "wall":
                buildWall(pos);
                break;
            case "house":
                buildSimpleHouse(pos);
                break;
            default:
                break;
        }
    }
    
    private void processBuilding() {
        if (currentTask == null) return;
        
        if (!isNearTarget(2.0)) {
            moveToTarget();
            return;
        }
        
        // 检查是否有所需材料
        if (!hasRequiredMaterial(currentTask.blockState)) {
            currentTask = null;
            return;
        }
        
        // 放置方块
        World world = aiPlayer.getWorld();
        if (world.getBlockState(currentTask.position).isAir()) {
            world.setBlockState(currentTask.position, currentTask.blockState);
        }
        
        currentTask = null;
        stopMoving();
    }
    
    private boolean hasRequiredMaterial(BlockState blockState) {
        // 简化的材料检查
        return true;
    }
    
    private void ensureBuildingMaterials() {
        // 给AI玩家一些基础建造材料
        aiPlayer.getInventory().insertStack(new ItemStack(Items.COBBLESTONE, 64));
        aiPlayer.getInventory().insertStack(new ItemStack(Items.OAK_PLANKS, 64));
    }
    
    private void buildWall(BlockPos start) {
        // 建造一面简单的墙
        for (int i = 0; i < 5; i++) {
            for (int j = 0; j < 3; j++) {
                BlockPos pos = start.add(i, j, 0);
                buildQueue.offer(new BuildTask(pos, Blocks.COBBLESTONE.getDefaultState()));
            }
        }
    }
    
    private void buildSimpleHouse(BlockPos start) {
        // 建造一个简单的房子
        // 地基
        for (int x = 0; x < 5; x++) {
            for (int z = 0; z < 5; z++) {
                buildQueue.offer(new BuildTask(start.add(x, 0, z), Blocks.COBBLESTONE.getDefaultState()));
            }
        }
        
        // 墙壁
        for (int y = 1; y <= 3; y++) {
            for (int x = 0; x < 5; x++) {
                buildQueue.offer(new BuildTask(start.add(x, y, 0), Blocks.COBBLESTONE.getDefaultState()));
                buildQueue.offer(new BuildTask(start.add(x, y, 4), Blocks.COBBLESTONE.getDefaultState()));
            }
            for (int z = 1; z < 4; z++) {
                buildQueue.offer(new BuildTask(start.add(0, y, z), Blocks.COBBLESTONE.getDefaultState()));
                buildQueue.offer(new BuildTask(start.add(4, y, z), Blocks.COBBLESTONE.getDefaultState()));
            }
        }
    }
    
    private static class BuildTask {
        final BlockPos position;
        final BlockState blockState;
        
        BuildTask(BlockPos position, BlockState blockState) {
            this.position = position;
            this.blockState = blockState;
        }
    }
}
