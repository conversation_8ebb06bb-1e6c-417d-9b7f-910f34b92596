package com.aiplayer.client.render;

import com.aiplayer.MinecraftAIPlayerMod;
import com.aiplayer.entity.AIPlayerEntity;
import net.minecraft.client.render.entity.EntityRendererFactory;
import net.minecraft.client.render.entity.MobEntityRenderer;
import net.minecraft.client.render.entity.model.PlayerEntityModel;
import net.minecraft.client.render.entity.model.EntityModelLayers;
import net.minecraft.util.Identifier;

/**
 * AI玩家实体渲染器
 * 负责渲染AI玩家实体的外观
 */
public class AIPlayerEntityRenderer extends MobEntityRenderer<AIPlayerEntity, PlayerEntityModel<AIPlayerEntity>> {
    
    private static final Identifier TEXTURE = MinecraftAIPlayerMod.id("textures/entity/ai_player.png");
    
    public AIPlayerEntityRenderer(EntityRendererFactory.Context context) {
        super(context, new PlayerEntityModel<>(context.getPart(EntityModelLayers.PLAYER), false), 0.5f);
    }
    
    @Override
    public Identifier getTexture(AIPlayerEntity entity) {
        return TEXTURE;
    }
}
